"""
Test the sensitivity factor functionality
"""
from playwright.sync_api import sync_playwright
import time
import os


def test_sensitivity_factor():
    """Test different sensitivity factor values"""
    
    print("Testing Sensitivity Factor for Drag Amount")
    print("=" * 60)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False, args=["--start-maximized"])
        page = browser.new_page()
        
        print("Navigating to HSAC website...")
        page.goto("https://hsac.org.in/eodb/")
        
        print("Waiting 10 seconds for map to load...")
        time.sleep(10)
        
        # Test parameters
        base_capture_size_mm = 60.0
        pixels_per_mm = 96 / 25.4  # ~3.78 pixels per mm
        capture_size_px = int(base_capture_size_mm * pixels_per_mm)  # ~227 pixels
        
        def capture_center_area(filename):
            """Capture the center area of the screen"""
            viewport_size = page.viewport_size
            center_x = viewport_size['width'] // 2
            center_y = viewport_size['height'] // 2
            
            capture_x = center_x - (capture_size_px // 2)
            capture_y = center_y - (capture_size_px // 2)
            
            # Ensure bounds
            capture_x = max(0, min(capture_x, viewport_size['width'] - capture_size_px))
            capture_y = max(0, min(capture_y, viewport_size['height'] - capture_size_px))
            
            page.screenshot(
                path=filename,
                clip={
                    'x': capture_x,
                    'y': capture_y,
                    'width': capture_size_px,
                    'height': capture_size_px
                }
            )
            print(f"✓ Captured center area: {filename}")
        
        def perform_sensitivity_drag(sensitivity, description):
            """Perform drag with given sensitivity factor"""
            print(f"\n{description} (Sensitivity: {sensitivity:.3f})")
            
            # Calculate adjusted drag amount
            base_drag_mm = base_capture_size_mm  # 60mm base
            adjusted_drag_mm = base_drag_mm * sensitivity
            
            print(f"  Base drag: {base_drag_mm}mm")
            print(f"  Adjusted drag: {adjusted_drag_mm:.3f}mm")
            
            try:
                # Find map element
                map_element = page.locator('#map').first
                if map_element.is_visible():
                    bbox = map_element.bounding_box()
                    drag_center_x = bbox['x'] + bbox['width'] // 2
                    drag_center_y = bbox['y'] + bbox['height'] // 2
                else:
                    viewport = page.viewport_size
                    drag_center_x = viewport['width'] // 2
                    drag_center_y = viewport['height'] // 2
                
                # Convert mm to pixels
                delta_x_px = int(-adjusted_drag_mm * pixels_per_mm)  # Negative = drag left
                delta_y_px = 0
                
                print(f"  Pixel movement: {delta_x_px}px x {delta_y_px}px")
                
                # Perform drag
                page.mouse.move(drag_center_x, drag_center_y)
                page.mouse.down()
                page.mouse.move(
                    drag_center_x + delta_x_px,
                    drag_center_y + delta_y_px,
                    steps=15
                )
                page.mouse.up()
                
                print(f"  ✓ Drag completed")
                return True
                
            except Exception as e:
                print(f"  ✗ Drag failed: {str(e)}")
                return False
        
        # Test different sensitivity factors
        print("\n" + "="*60)
        print("TESTING DIFFERENT SENSITIVITY FACTORS")
        print("="*60)
        
        # Capture initial position
        print("\n1. Capturing initial center position...")
        capture_center_area("sensitivity_initial.png")
        
        # Test sensitivity 0.500 (half movement)
        print("\n2. Testing sensitivity 0.500 (half movement)...")
        if perform_sensitivity_drag(0.500, "Half sensitivity drag"):
            time.sleep(4)
            capture_center_area("sensitivity_0500.png")
        
        # Test sensitivity 1.000 (normal movement)
        print("\n3. Testing sensitivity 1.000 (normal movement)...")
        if perform_sensitivity_drag(1.000, "Normal sensitivity drag"):
            time.sleep(4)
            capture_center_area("sensitivity_1000.png")
        
        # Test sensitivity 2.000 (double movement)
        print("\n4. Testing sensitivity 2.000 (double movement)...")
        if perform_sensitivity_drag(2.000, "Double sensitivity drag"):
            time.sleep(4)
            capture_center_area("sensitivity_2000.png")
        
        # Test sensitivity 0.300 (fine adjustment)
        print("\n5. Testing sensitivity 0.300 (fine adjustment)...")
        if perform_sensitivity_drag(0.300, "Fine adjustment drag"):
            time.sleep(4)
            capture_center_area("sensitivity_0300.png")
        
        # Calculate expected movements
        print("\n" + "="*60)
        print("SENSITIVITY CALCULATIONS")
        print("="*60)
        
        sensitivity_tests = [0.300, 0.500, 1.000, 2.000, 3.000]
        
        for sensitivity in sensitivity_tests:
            base_mm = 60.0
            adjusted_mm = base_mm * sensitivity
            adjusted_px = int(adjusted_mm * pixels_per_mm)
            
            print(f"Sensitivity {sensitivity:.3f}: {base_mm}mm × {sensitivity:.3f} = {adjusted_mm:.3f}mm = {adjusted_px}px")
        
        # Check results
        print("\n" + "="*60)
        print("CAPTURE RESULTS")
        print("="*60)
        
        test_files = [
            "sensitivity_initial.png",
            "sensitivity_0500.png",
            "sensitivity_1000.png", 
            "sensitivity_2000.png",
            "sensitivity_0300.png"
        ]
        
        for filename in test_files:
            if os.path.exists(filename):
                size = os.path.getsize(filename)
                print(f"✓ {filename}: {size} bytes")
            else:
                print(f"✗ {filename}: Missing")
        
        success_count = sum(1 for f in test_files if os.path.exists(f))
        print(f"\nCapture success rate: {success_count}/{len(test_files)}")
        
        if success_count == len(test_files):
            print("✓ All sensitivity tests successful!")
            print("✓ Sensitivity factor working correctly")
        else:
            print("⚠ Some sensitivity tests failed")
        
        print("\nSENSITIVITY USAGE GUIDE:")
        print("• For high zoom (detailed view): Use 0.300-0.700")
        print("• For medium zoom (normal view): Use 0.800-1.200") 
        print("• For low zoom (wide view): Use 1.500-3.000")
        print("• Start with 1.000 and adjust based on overlap/gaps")
        
        print("\nTest complete. Browser will close in 15 seconds...")
        print("You can inspect the captured images to see sensitivity effects.")
        time.sleep(15)
        
        browser.close()


if __name__ == "__main__":
    test_sensitivity_factor()
