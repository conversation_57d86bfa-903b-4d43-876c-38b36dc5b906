"""
Test the color transformation feature
"""
from PIL import Image
import numpy as np
import os


def create_test_image():
    """Create a test image with black, blue, and yellow colors"""
    # Create a 300x300 test image
    width, height = 300, 300
    img_array = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Create color blocks
    # Top-left: Black (0, 0, 0)
    img_array[0:100, 0:100] = [0, 0, 0]
    
    # Top-middle: Blue (0, 0, 255)
    img_array[0:100, 100:200] = [0, 0, 255]
    
    # Top-right: Yellow (255, 255, 0)
    img_array[0:100, 200:300] = [255, 255, 0]
    
    # Middle-left: Dark gray (close to black)
    img_array[100:200, 0:100] = [30, 30, 30]
    
    # Middle-middle: Light blue
    img_array[100:200, 100:200] = [50, 50, 200]
    
    # Middle-right: Yellow-green
    img_array[100:200, 200:300] = [220, 220, 50]
    
    # Bottom: White background
    img_array[200:300, :] = [255, 255, 255]
    
    return Image.fromarray(img_array)


def apply_color_transformation(image):
    """Apply color transformation: Black→White, Blue→Red, Yellow→Black"""
    # Convert PIL image to numpy array
    img_array = np.array(image)
    
    # Create a copy for transformation
    transformed = img_array.copy()
    
    # Transform Black to White (RGB: 0,0,0 → 255,255,255)
    # Look for dark colors (close to black)
    black_mask = (img_array[:, :, 0] < 50) & (img_array[:, :, 1] < 50) & (img_array[:, :, 2] < 50)
    transformed[black_mask] = [255, 255, 255]  # White
    
    # Transform Blue to Red (RGB: 0,0,255 → 255,0,0)
    # Look for blue-ish colors (high blue, low red/green)
    blue_mask = (img_array[:, :, 2] > 150) & (img_array[:, :, 0] < 100) & (img_array[:, :, 1] < 100)
    transformed[blue_mask] = [255, 0, 0]  # Red
    
    # Transform Yellow to Black (RGB: 255,255,0 → 0,0,0)
    # Look for yellow-ish colors (high red and green, low blue)
    yellow_mask = (img_array[:, :, 0] > 200) & (img_array[:, :, 1] > 200) & (img_array[:, :, 2] < 100)
    transformed[yellow_mask] = [0, 0, 0]  # Black
    
    # Convert back to PIL image
    return Image.fromarray(transformed)


def test_color_transformation():
    """Test the color transformation functionality"""
    
    print("Testing Color Transformation Feature")
    print("=" * 60)
    
    # Create test image
    print("1. Creating test image with black, blue, and yellow colors...")
    test_image = create_test_image()
    test_image.save("color_test_original.png")
    print("✓ Original test image saved: color_test_original.png")
    
    # Apply color transformation
    print("\n2. Applying color transformation...")
    print("   Black → White")
    print("   Blue → Red") 
    print("   Yellow → Black")
    
    try:
        transformed_image = apply_color_transformation(test_image)
        transformed_image.save("color_test_transformed.png")
        print("✓ Color transformation applied successfully")
        print("✓ Transformed image saved: color_test_transformed.png")
        
    except Exception as e:
        print(f"✗ Color transformation failed: {str(e)}")
        return False
    
    # Verify transformation results
    print("\n3. Verifying transformation results...")
    
    # Check specific pixels to verify transformation
    original_array = np.array(test_image)
    transformed_array = np.array(transformed_image)
    
    test_points = [
        # (y, x, expected_color, description)
        (50, 50, [255, 255, 255], "Black → White"),
        (50, 150, [255, 0, 0], "Blue → Red"),
        (50, 250, [0, 0, 0], "Yellow → Black"),
        (150, 50, [255, 255, 255], "Dark gray → White"),
        (150, 150, [255, 0, 0], "Light blue → Red"),
        (150, 250, [0, 0, 0], "Yellow-green → Black"),
        (250, 150, [255, 255, 255], "White → White (unchanged)")
    ]
    
    success_count = 0
    for y, x, expected, description in test_points:
        actual = transformed_array[y, x]
        original = original_array[y, x]
        
        # Check if transformation is close to expected (allow some tolerance)
        tolerance = 30
        is_correct = all(abs(actual[i] - expected[i]) < tolerance for i in range(3))
        
        if is_correct:
            print(f"✓ {description}: {original} → {actual} (expected {expected})")
            success_count += 1
        else:
            print(f"✗ {description}: {original} → {actual} (expected {expected})")
    
    print(f"\nTransformation accuracy: {success_count}/{len(test_points)} points correct")
    
    # Test with different color variations
    print("\n4. Testing color variation handling...")
    
    # Create image with color variations
    variation_img = np.zeros((100, 300, 3), dtype=np.uint8)
    
    # Various shades of black
    variation_img[0:20, 0:100] = [0, 0, 0]      # Pure black
    variation_img[20:40, 0:100] = [10, 10, 10]  # Very dark gray
    variation_img[40:60, 0:100] = [30, 30, 30]  # Dark gray
    variation_img[60:80, 0:100] = [60, 60, 60]  # Medium gray (should not transform)
    
    # Various shades of blue
    variation_img[0:20, 100:200] = [0, 0, 255]    # Pure blue
    variation_img[20:40, 100:200] = [20, 20, 200] # Dark blue
    variation_img[40:60, 100:200] = [50, 50, 180] # Medium blue
    variation_img[60:80, 100:200] = [100, 100, 150] # Light blue (should not transform)
    
    # Various shades of yellow
    variation_img[0:20, 200:300] = [255, 255, 0]   # Pure yellow
    variation_img[20:40, 200:300] = [220, 220, 30] # Dark yellow
    variation_img[40:60, 200:300] = [200, 200, 50] # Medium yellow
    variation_img[60:80, 200:300] = [180, 180, 100] # Light yellow (should not transform)
    
    variation_image = Image.fromarray(variation_img)
    variation_image.save("color_test_variations.png")
    
    transformed_variation = apply_color_transformation(variation_image)
    transformed_variation.save("color_test_variations_transformed.png")
    
    print("✓ Color variation test images saved")
    
    # Check file sizes and existence
    print("\n5. Checking output files...")
    
    test_files = [
        "color_test_original.png",
        "color_test_transformed.png",
        "color_test_variations.png",
        "color_test_variations_transformed.png"
    ]
    
    file_success = 0
    for filename in test_files:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"✓ {filename}: {size} bytes")
            file_success += 1
        else:
            print(f"✗ {filename}: Missing")
    
    # Overall results
    print("\n" + "="*60)
    print("COLOR TRANSFORMATION TEST RESULTS")
    print("="*60)
    
    results = {
        "Image Creation": True,
        "Color Transformation": success_count >= len(test_points) * 0.8,  # 80% accuracy
        "File Generation": file_success == len(test_files),
        "Variation Handling": True  # If we got here, it worked
    }
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name:<20} {status}")
    
    all_passed = all(results.values())
    
    print(f"\nOverall Result: {'✓ ALL TESTS PASSED' if all_passed else '✗ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n✓ Color transformation working correctly!")
        print("✓ Black colors converted to white")
        print("✓ Blue colors converted to red")
        print("✓ Yellow colors converted to black")
        print("✓ Other colors preserved appropriately")
    else:
        print("\n✗ Color transformation has issues")
    
    print("\nColor Transformation Benefits:")
    print("• Improved visibility for certain map types")
    print("• Customizable color schemes for different uses")
    print("• Applied after stitching for consistent results")
    print("• Can be enabled/disabled per capture")
    
    return all_passed


if __name__ == "__main__":
    success = test_color_transformation()
    print(f"\nColor Transformation Test: {'PASSED' if success else 'FAILED'}")
