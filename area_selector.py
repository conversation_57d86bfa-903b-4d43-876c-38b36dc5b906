"""
Area Selection Tool for Map Capture
Allows user to visually select the starting area for grid capture
"""
import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk, ImageDraw
import os


class AreaSelector:
    """Visual area selection tool for map capture"""
    
    def __init__(self, parent, screenshot_path, capture_size_px, grid_rows, grid_cols, callback):
        self.parent = parent
        self.screenshot_path = screenshot_path
        self.capture_size_px = capture_size_px
        self.grid_rows = grid_rows
        self.grid_cols = grid_cols
        self.callback = callback
        
        # Selection state
        self.selection_start = None
        self.selection_end = None
        self.selected_area = None
        
        # Image scaling
        self.scale_factor = 1.0
        self.display_width = 800
        self.display_height = 600
        
        self.create_window()
        self.load_screenshot()
        
    def create_window(self):
        """Create the area selection window"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("🎯 Select Capture Area - Moond Sahab")
        self.window.geometry("900x700")
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # Instructions
        instructions = ttk.Label(self.window, 
                                text="📍 Click and drag to select the starting area for your grid capture",
                                font=("Arial", 12, "bold"))
        instructions.pack(pady=10)
        
        # Info frame
        info_frame = ttk.Frame(self.window)
        info_frame.pack(fill="x", padx=20, pady=5)
        
        self.info_label = ttk.Label(info_frame, 
                                   text=f"Grid: {self.grid_rows}×{self.grid_cols} | Capture Size: {self.capture_size_px}px",
                                   font=("Arial", 10))
        self.info_label.pack(side="left")
        
        # Canvas frame
        canvas_frame = ttk.Frame(self.window)
        canvas_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # Canvas with scrollbars
        self.canvas = tk.Canvas(canvas_frame, bg="white", cursor="crosshair")
        
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview)
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient="horizontal", command=self.canvas.xview)
        
        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid layout
        self.canvas.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)
        
        # Bind mouse events
        self.canvas.bind("<Button-1>", self.on_mouse_down)
        self.canvas.bind("<B1-Motion>", self.on_mouse_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_mouse_up)
        
        # Control buttons
        button_frame = ttk.Frame(self.window)
        button_frame.pack(fill="x", padx=20, pady=10)
        
        ttk.Button(button_frame, text="🔄 Reset Selection", 
                  command=self.reset_selection).pack(side="left", padx=(0, 10))
        
        ttk.Button(button_frame, text="📷 Take New Screenshot", 
                  command=self.take_new_screenshot).pack(side="left", padx=(0, 10))
        
        ttk.Button(button_frame, text="✅ Confirm Selection", 
                  command=self.confirm_selection).pack(side="right", padx=(10, 0))
        
        ttk.Button(button_frame, text="❌ Cancel", 
                  command=self.cancel_selection).pack(side="right")
        
        # Status label
        self.status_label = ttk.Label(self.window, 
                                     text="Click and drag to select the starting area for grid capture",
                                     font=("Arial", 9), foreground="blue")
        self.status_label.pack(pady=5)
        
    def load_screenshot(self):
        """Load and display the screenshot"""
        try:
            if not os.path.exists(self.screenshot_path):
                messagebox.showerror("Error", f"Screenshot not found: {self.screenshot_path}")
                self.window.destroy()
                return
                
            # Load image
            self.original_image = Image.open(self.screenshot_path)
            img_width, img_height = self.original_image.size
            
            # Calculate scale factor to fit in display area
            self.scale_factor = min(self.display_width / img_width, self.display_height / img_height)
            
            # Scale image for display
            display_width = int(img_width * self.scale_factor)
            display_height = int(img_height * self.scale_factor)
            
            self.display_image = self.original_image.resize((display_width, display_height), Image.Resampling.LANCZOS)
            self.photo = ImageTk.PhotoImage(self.display_image)
            
            # Update canvas
            self.canvas.configure(scrollregion=(0, 0, display_width, display_height))
            self.canvas.create_image(0, 0, anchor="nw", image=self.photo, tags="screenshot")
            
            # Draw grid overlay
            self.draw_grid_overlay()
            
            self.status_label.config(text=f"Screenshot loaded: {img_width}×{img_height} pixels (scaled {self.scale_factor:.2f}x)")
            
        except Exception as e:
            messagebox.showerror("Error", f"Could not load screenshot: {str(e)}")
            self.window.destroy()
            
    def draw_grid_overlay(self):
        """Draw grid overlay to show capture areas"""
        if not hasattr(self, 'display_image'):
            return
            
        img_width, img_height = self.display_image.size
        
        # Calculate grid cell size
        cell_width = self.capture_size_px * self.scale_factor
        cell_height = self.capture_size_px * self.scale_factor
        
        # Draw grid lines
        for row in range(self.grid_rows + 1):
            y = row * cell_height
            if y <= img_height:
                self.canvas.create_line(0, y, img_width, y, fill="lightblue", width=1, tags="grid")
                
        for col in range(self.grid_cols + 1):
            x = col * cell_width
            if x <= img_width:
                self.canvas.create_line(x, 0, x, img_height, fill="lightblue", width=1, tags="grid")
        
        # Draw grid cell numbers
        for row in range(self.grid_rows):
            for col in range(self.grid_cols):
                x = col * cell_width + cell_width // 2
                y = row * cell_height + cell_height // 2
                
                if x < img_width and y < img_height:
                    cell_num = row * self.grid_cols + col + 1
                    self.canvas.create_text(x, y, text=str(cell_num), 
                                          fill="blue", font=("Arial", 12, "bold"), tags="grid")
    
    def on_mouse_down(self, event):
        """Handle mouse down event"""
        # Convert canvas coordinates to image coordinates
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)
        
        self.selection_start = (canvas_x, canvas_y)
        self.selection_end = (canvas_x, canvas_y)
        
        # Clear previous selection
        self.canvas.delete("selection")
        
    def on_mouse_drag(self, event):
        """Handle mouse drag event"""
        if self.selection_start is None:
            return
            
        # Convert canvas coordinates to image coordinates
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)
        
        self.selection_end = (canvas_x, canvas_y)
        
        # Clear previous selection rectangle
        self.canvas.delete("selection")
        
        # Draw selection rectangle
        x1, y1 = self.selection_start
        x2, y2 = self.selection_end
        
        self.canvas.create_rectangle(x1, y1, x2, y2, 
                                   outline="red", width=2, tags="selection")
        
        # Update status
        width = abs(x2 - x1) / self.scale_factor
        height = abs(y2 - y1) / self.scale_factor
        self.status_label.config(text=f"Selection: {width:.0f}×{height:.0f} pixels")
        
    def on_mouse_up(self, event):
        """Handle mouse up event"""
        if self.selection_start is None:
            return
            
        # Convert canvas coordinates to image coordinates
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)
        
        self.selection_end = (canvas_x, canvas_y)
        
        # Calculate selection in original image coordinates
        x1, y1 = self.selection_start
        x2, y2 = self.selection_end
        
        # Convert to original image coordinates
        orig_x1 = int(min(x1, x2) / self.scale_factor)
        orig_y1 = int(min(y1, y2) / self.scale_factor)
        orig_x2 = int(max(x1, x2) / self.scale_factor)
        orig_y2 = int(max(y1, y2) / self.scale_factor)
        
        self.selected_area = {
            'x': orig_x1,
            'y': orig_y1,
            'width': orig_x2 - orig_x1,
            'height': orig_y2 - orig_y1,
            'center_x': (orig_x1 + orig_x2) // 2,
            'center_y': (orig_y1 + orig_y2) // 2
        }
        
        # Update status
        self.status_label.config(
            text=f"Selected area: {self.selected_area['width']}×{self.selected_area['height']} pixels at ({self.selected_area['x']}, {self.selected_area['y']})",
            foreground="green"
        )
        
    def reset_selection(self):
        """Reset the current selection"""
        self.canvas.delete("selection")
        self.selection_start = None
        self.selection_end = None
        self.selected_area = None
        self.status_label.config(text="Selection cleared. Click and drag to select area.", foreground="blue")
        
    def take_new_screenshot(self):
        """Take a new screenshot for selection"""
        self.status_label.config(text="Taking new screenshot...", foreground="orange")
        self.window.update()
        
        # This would trigger a new screenshot in the main application
        # For now, just reload the existing one
        self.load_screenshot()
        self.reset_selection()
        
    def confirm_selection(self):
        """Confirm the selected area"""
        if self.selected_area is None:
            messagebox.showwarning("No Selection", "Please select an area first by clicking and dragging.")
            return
            
        # Validate selection size
        min_size = self.capture_size_px // 2
        if (self.selected_area['width'] < min_size or 
            self.selected_area['height'] < min_size):
            messagebox.showwarning("Selection Too Small", 
                                 f"Selected area is too small. Minimum size: {min_size}×{min_size} pixels")
            return
            
        # Call callback with selected area
        self.callback(self.selected_area)
        self.window.destroy()
        
    def cancel_selection(self):
        """Cancel area selection"""
        self.callback(None)
        self.window.destroy()


def test_area_selector():
    """Test the area selector with a sample image"""
    root = tk.Tk()
    root.withdraw()  # Hide main window
    
    # Create a test image if none exists
    test_image_path = "test_screenshot.png"
    if not os.path.exists(test_image_path):
        # Create a simple test image
        img = Image.new('RGB', (800, 600), color='lightblue')
        draw = ImageDraw.Draw(img)
        
        # Draw some test content
        draw.rectangle([100, 100, 300, 200], fill='red', outline='black')
        draw.rectangle([400, 200, 600, 400], fill='green', outline='black')
        draw.text((350, 50), "Test Map Area", fill='black')
        
        img.save(test_image_path)
    
    def selection_callback(selected_area):
        if selected_area:
            print(f"Selected area: {selected_area}")
        else:
            print("Selection cancelled")
        root.quit()
    
    # Create area selector
    selector = AreaSelector(root, test_image_path, 60, 3, 3, selection_callback)
    
    root.mainloop()


if __name__ == "__main__":
    test_area_selector()
