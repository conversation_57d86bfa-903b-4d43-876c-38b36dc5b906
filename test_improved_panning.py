"""
Test the improved ArcGIS panning logic
"""
from playwright.sync_api import sync_playwright
import time


def test_improved_panning():
    """Test the improved panning calculation"""
    
    # Updated JavaScript with improved calculation
    test_pan_js = '''
    (() => {
        const deltaX = -100;
        const deltaY = -50;
        
        try {
            if (window.view && window.view.goTo && window.view.ready) {
                const currentCenter = window.view.center;
                const currentExtent = window.view.extent;
                
                // Calculate map units per pixel (more accurate)
                const mapUnitsPerPixelX = currentExtent.width / window.view.width;
                const mapUnitsPerPixelY = currentExtent.height / window.view.height;
                
                // Calculate offset in map units
                const offsetX = deltaX * mapUnitsPerPixelX;
                const offsetY = deltaY * mapUnitsPerPixelY;
                
                // Calculate new center
                const newCenterX = currentCenter.longitude + offsetX;
                const newCenterY = currentCenter.latitude - offsetY;
                
                return {
                    success: true,
                    method: 'arcgis_improved',
                    currentCenter: [currentCenter.longitude, currentCenter.latitude],
                    newCenter: [newCenterX, newCenterY],
                    mapUnitsPerPixelX: mapUnitsPerPixelX,
                    mapUnitsPerPixelY: mapUnitsPerPixelY,
                    offsetX: offsetX,
                    offsetY: offsetY,
                    extentWidth: currentExtent.width,
                    extentHeight: currentExtent.height,
                    viewWidth: window.view.width,
                    viewHeight: window.view.height
                };
            } else {
                return {
                    success: false,
                    error: 'ArcGIS view not ready',
                    viewExists: !!window.view,
                    viewReady: window.view ? window.view.ready : false
                };
            }
        } catch (e) {
            return {
                success: false,
                error: e.message
            };
        }
    })()
    '''
    
    print("Testing Improved ArcGIS Panning Logic")
    print("=" * 50)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False, args=["--start-maximized"])
        page = browser.new_page()
        
        print("Navigating to HSAC website...")
        page.goto("https://hsac.org.in/eodb/")
        
        print("Waiting for map to load...")
        time.sleep(10)
        
        print("Testing improved panning calculation...")
        result = page.evaluate(test_pan_js)
        
        print("\nResults:")
        print(f"Success: {result.get('success')}")
        
        if result.get('success'):
            print(f"Method: {result.get('method')}")
            print(f"Current center: {result.get('currentCenter')}")
            print(f"New center: {result.get('newCenter')}")
            print(f"Map units per pixel X: {result.get('mapUnitsPerPixelX'):.6f}")
            print(f"Map units per pixel Y: {result.get('mapUnitsPerPixelY'):.6f}")
            print(f"Offset X: {result.get('offsetX'):.6f}")
            print(f"Offset Y: {result.get('offsetY'):.6f}")
            print(f"Extent: {result.get('extentWidth'):.2f} x {result.get('extentHeight'):.2f}")
            print(f"View: {result.get('viewWidth')} x {result.get('viewHeight')}")
            
            # Check if coordinates are reasonable
            old_center = result.get('currentCenter', [0, 0])
            new_center = result.get('newCenter', [0, 0])
            
            lon_diff = abs(new_center[0] - old_center[0])
            lat_diff = abs(new_center[1] - old_center[1])
            
            print(f"\nCoordinate differences:")
            print(f"Longitude: {lon_diff:.6f} degrees")
            print(f"Latitude: {lat_diff:.6f} degrees")
            
            if lon_diff < 1 and lat_diff < 1:
                print("✓ Coordinate changes look reasonable!")
            else:
                print("⚠ Coordinate changes seem too large")
                
        else:
            print(f"Error: {result.get('error')}")
            print(f"View exists: {result.get('viewExists')}")
            print(f"View ready: {result.get('viewReady')}")
        
        print("\nTest complete. Browser will close in 5 seconds...")
        time.sleep(5)
        
        browser.close()


if __name__ == "__main__":
    test_improved_panning()
