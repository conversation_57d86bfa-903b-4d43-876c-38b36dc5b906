# Column-by-Column Grid Traversal Logic

## Overview

The application now uses **column-by-column traversal** instead of row-by-row traversal. This approach is more logical for map capture as it completes each vertical column from top to bottom before moving to the next column.

## Grid Traversal Order

### 3×3 Grid Example
```
Traditional Row-by-Row:     New Column-by-Column:
1 → 2 → 3                   1   4   7
↓       ↓                   ↓   ↓   ↓
4 → 5 → 6                   2   5   8
↓       ↓                   ↓   ↓   ↓
7 → 8 → 9                   3   6   9
```

### Capture Sequence
**Column-by-Column Order**: (0,0) → (1,0) → (2,0) → (0,1) → (1,1) → (2,1) → (0,2) → (1,2) → (2,2)

1. **Column 1**: Capture (0,0), (1,0), (2,0) - top to bottom
2. **Column 2**: Capture (0,1), (1,1), (2,1) - top to bottom  
3. **Column 3**: Capture (0,2), (1,2), (2,2) - top to bottom

## Dragging Logic

### Movement Rules

#### 1. Moving Down Within Column
**Action**: Drag UP to reveal bottom area
**Logic**: When dragging UP, the bottom portion of the map comes to center
**Code**: `drag_y_mm = base_drag_amount` (positive = drag up)

#### 2. Moving to Next Column
**Action**: Two-step process
1. **Reset to Top**: Drag DOWN to return to top of map
2. **Move Right**: Drag LEFT to reveal right area

**Code**:
```python
# Reset to top position
reset_drag_y_mm = (rows - 1) * base_drag_amount  # Drag down
# Move to next column  
drag_x_mm = -base_drag_amount  # Drag left to show right
```

### Drag Direction Reference

| Movement | Drag Direction | Result |
|----------|---------------|---------|
| **Show Bottom Area** | Drag UP ↑ | Bottom portion comes to center |
| **Show Right Area** | Drag LEFT ← | Right portion comes to center |
| **Reset to Top** | Drag DOWN ↓ | Top portion comes to center |
| **Reset to Left** | Drag RIGHT → | Left portion comes to center |

## Implementation Details

### Loop Structure
```python
for col in range(params['cols']):           # Outer loop: columns
    for row in range(params['rows']):       # Inner loop: rows
        if row == 0 and col == 0:
            continue  # Skip first tile (already captured)
            
        if row == 0 and col > 0:
            # Moving to next column (starting from top)
            reset_to_top()
            move_to_next_column()
        else:
            # Moving down within same column
            move_down_in_column()
            
        capture_tile(row, col)
```

### Drag Calculations
```python
base_drag_amount = capture_size_mm * sensitivity

# Moving down in column
drag_x_mm = 0
drag_y_mm = base_drag_amount  # Drag up to show bottom

# Moving to next column
reset_drag_y_mm = (rows - 1) * base_drag_amount  # Reset to top
drag_x_mm = -base_drag_amount  # Move to right area
```

## Stitching Logic

### Image Placement
The stitching logic remains the same because tiles are still named with (row, col) coordinates:

```python
for row in range(params['rows']):
    for col in range(params['cols']):
        filename = f"tile_{row}_{col}.png"
        x_offset = col * params['width']   # Column position
        y_offset = row * params['height']  # Row position
        final_image.paste(tile_image, (x_offset, y_offset))
```

### Position Mapping
- **Tile (0,0)**: Top-left corner
- **Tile (0,1)**: Top-middle  
- **Tile (0,2)**: Top-right
- **Tile (1,0)**: Middle-left
- **Tile (1,1)**: Center
- **Tile (1,2)**: Middle-right
- **Tile (2,0)**: Bottom-left
- **Tile (2,1)**: Bottom-middle
- **Tile (2,2)**: Bottom-right

## Benefits of Column-by-Column Traversal

### 1. Logical Flow
- **Natural progression**: Top to bottom feels more intuitive
- **Consistent direction**: Always moving down within columns
- **Predictable pattern**: Easy to understand and debug

### 2. Better Map Handling
- **Reduced resets**: Only reset position when changing columns
- **Smoother movement**: Continuous downward movement within columns
- **Less confusion**: Clear directional logic

### 3. Improved Debugging
- **Clear progress**: "Column X, Row Y" status messages
- **Easy verification**: Can visually track column completion
- **Better error handling**: Isolated column failures

## Status Messages

### Progress Tracking
```
✓ Captured tile (0,0) - Column 1, Row 1 of 9 total
Moving down in column 1 to row 2: 60.000mm up to show bottom area
✓ Captured tile (1,0) - Column 1, Row 2 (2/9)
Moving down in column 1 to row 3: 60.000mm up to show bottom area  
✓ Captured tile (2,0) - Column 1, Row 3 (3/9)
Moving to column 2: Reset 120.000mm down to top, then 60.000mm left to show right area
✓ Captured tile (0,1) - Column 2, Row 1 (4/9)
```

### Movement Descriptions
- **"Moving down in column X to row Y"**: Vertical movement within column
- **"Moving to column X"**: Horizontal movement to new column
- **"Reset to top"**: Vertical reset before column change
- **"Show bottom/right area"**: Explanation of drag direction

## Testing and Verification

### Test Grid Patterns
```python
# 2×2 Grid Test
expected_order = [(0,0), (1,0), (0,1), (1,1)]

# 3×3 Grid Test  
expected_order = [(0,0), (1,0), (2,0), (0,1), (1,1), (2,1), (0,2), (1,2), (2,2)]

# 4×4 Grid Test
expected_order = [(0,0), (1,0), (2,0), (3,0), (0,1), (1,1), (2,1), (3,1), 
                  (0,2), (1,2), (2,2), (3,2), (0,3), (1,3), (2,3), (3,3)]
```

### Verification Points
1. **Capture Order**: Verify tiles captured in column-by-column sequence
2. **Drag Directions**: Confirm UP for down movement, LEFT for right movement
3. **Reset Logic**: Check proper reset to top when changing columns
4. **Stitching Result**: Ensure final image has correct tile placement

## Troubleshooting

### Common Issues

#### Tiles Out of Order
**Symptoms**: Final image has misplaced tiles
**Cause**: Incorrect loop structure or drag logic
**Solution**: Verify column-by-column loop implementation

#### Wrong Drag Direction
**Symptoms**: Map moves opposite to expected direction
**Cause**: Incorrect drag direction calculation
**Solution**: Remember: drag UP to show bottom, drag LEFT to show right

#### Reset Failures
**Symptoms**: Column transitions don't work properly
**Cause**: Incorrect reset drag calculation
**Solution**: Verify reset_drag_y_mm = (rows - 1) * base_drag_amount

### Debug Tips
1. **Test with 2×2 grid**: Simplest case to verify logic
2. **Check status messages**: Verify movement descriptions
3. **Examine captured files**: Confirm expected content in each tile
4. **Monitor drag amounts**: Ensure sensitivity factor is applied correctly

## Conclusion

The column-by-column traversal provides a more logical and intuitive approach to grid capture. By completing each column from top to bottom before moving to the next column, the application creates a predictable and efficient capture pattern that is easier to understand, debug, and optimize.

**Key Advantages**:
- ✅ **Intuitive movement pattern**
- ✅ **Consistent directional logic**  
- ✅ **Better progress tracking**
- ✅ **Easier debugging and verification**
- ✅ **More natural map exploration flow**
