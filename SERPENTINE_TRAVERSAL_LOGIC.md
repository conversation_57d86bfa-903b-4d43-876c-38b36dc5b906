# Serpentine Grid Traversal Logic

## Overview

The application now uses **serpentine/zigzag traversal** for optimal grid capture efficiency. This pattern alternates column directions to eliminate unnecessary resets and minimize total movement.

## Serpentine Pattern

### 3×3 Grid Example
```
Traditional Column-by-Column:    Optimized Serpentine:
1   4   7                        1   6   7
↓   ↓   ↓                        ↓   ↑   ↓
2   5   8                        2   5   8
↓   ↓   ↓                        ↓   ↑   ↓
3   6   9                        3   4   9
```

### Capture Sequence
**Serpentine Order**: (0,0) → (1,0) → (2,0) → (2,1) → (1,1) → (0,1) → (0,2) → (1,2) → (2,2)

1. **Column 1 (↓)**: (0,0) → (1,0) → (2,0) - Top to bottom
2. **Column 2 (↑)**: (2,1) → (1,1) → (0,1) - Bottom to top
3. **Column 3 (↓)**: (0,2) → (1,2) → (2,2) - Top to bottom

## Movement Efficiency

### Traditional vs Serpentine
```
Traditional Column-by-Column:
Col1: (0,0)→(1,0)→(2,0) = 2 moves
Reset: Drag to top = 1 move
Col2: (0,1)→(1,1)→(2,1) = 3 moves  
Reset: <PERSON>ag to top = 1 move
Col3: (0,2)→(1,2)→(2,2) = 3 moves
Total: 10 moves

Serpentine:
Col1: (0,0)→(1,0)→(2,0) = 2 moves
Col2: (2,1)→(1,1)→(0,1) = 3 moves (no reset!)
Col3: (0,2)→(1,2)→(2,2) = 3 moves (no reset!)
Total: 8 moves (20% reduction)
```

### Benefits
- ✅ **No vertical resets** between columns
- ✅ **Continuous movement** within columns
- ✅ **Minimal backtracking** 
- ✅ **Optimal path efficiency**

## Dragging Logic

### Movement Rules

#### 1. Even Columns (0, 2, 4...) - Top to Bottom ↓
- **Start**: Top of column
- **Direction**: Move down
- **Drag**: DOWN to show bottom area
- **Logic**: Drag DOWN → bottom portion comes to center

#### 2. Odd Columns (1, 3, 5...) - Bottom to Top ↑
- **Start**: Bottom of column (from previous column)
- **Direction**: Move up
- **Drag**: UP to show top area
- **Logic**: Drag UP → top portion comes to center

#### 3. Column Transitions
- **Action**: Drag LEFT to show right area
- **No vertical reset** needed
- **Seamless transition** from end of one column to start of next

### Drag Direction Reference

| Movement | Column Type | Drag Direction | Result |
|----------|-------------|---------------|---------|
| **Move Down** | Even (↓) | Drag UP ↑ | Bottom area to center |
| **Move Up** | Odd (↑) | Drag DOWN ↓ | Top area to center |
| **Next Column** | Any | Drag LEFT ← | Right area to center |

## Implementation Details

### Loop Structure
```python
for col in range(params['cols']):
    # Determine direction for this column
    if col % 2 == 0:
        # Even column: top to bottom
        row_range = range(params['rows'])
        direction = "top-to-bottom"
    else:
        # Odd column: bottom to top
        row_range = range(params['rows'] - 1, -1, -1)
        direction = "bottom-to-top"
    
    for i, row in enumerate(row_range):
        if i == 0 and col > 0:
            # Moving to start of next column
            drag_horizontally()
        else:
            # Moving within column
            drag_vertically()
        
        capture_tile(row, col)
```

### Drag Calculations
```python
base_drag_amount = capture_size_mm * sensitivity

# Moving to next column
if i == 0 and col > 0:
    drag_x_mm = -base_drag_amount  # Left to show right
    drag_y_mm = 0

# Moving within column
else:
    drag_x_mm = 0
    if col % 2 == 0:
        # Even column: moving down
        drag_y_mm = base_drag_amount  # Up to show bottom
    else:
        # Odd column: moving up  
        drag_y_mm = -base_drag_amount  # Down to show top
```

## Status Messages

### Progress Tracking
```
✓ Captured tile (0,0) - Column 1↓, Row 1 of 9 total (serpentine pattern)
Moving down in column 1 to row 2: 60.000mm up to show bottom area
✓ Captured tile (1,0) - Column 1↓, Row 2 (2/9)
Moving down in column 1 to row 3: 60.000mm up to show bottom area
✓ Captured tile (2,0) - Column 1↓, Row 3 (3/9)
Moving to column 2 (bottom-to-top): 60.000mm left to show right area
✓ Captured tile (2,1) - Column 2↑, Row 3 (4/9)
Moving up in column 2 to row 2: 60.000mm down to show top area
✓ Captured tile (1,1) - Column 2↑, Row 2 (5/9)
```

### Direction Indicators
- **Column 1↓**: Even column, top-to-bottom direction
- **Column 2↑**: Odd column, bottom-to-top direction
- **Column 3↓**: Even column, top-to-bottom direction

## Grid Size Examples

### 2×2 Grid
**Order**: (0,0) → (1,0) → (1,1) → (0,1)
**Pattern**: Col1↓ Col2↑
**Moves**: 3 total (vs 4 with resets)

### 3×3 Grid  
**Order**: (0,0) → (1,0) → (2,0) → (2,1) → (1,1) → (0,1) → (0,2) → (1,2) → (2,2)
**Pattern**: Col1↓ Col2↑ Col3↓
**Moves**: 8 total (vs 10 with resets)

### 4×4 Grid
**Order**: (0,0) → (1,0) → (2,0) → (3,0) → (3,1) → (2,1) → (1,1) → (0,1) → (0,2) → (1,2) → (2,2) → (3,2) → (3,3) → (2,3) → (1,3) → (0,3)
**Pattern**: Col1↓ Col2↑ Col3↓ Col4↑
**Moves**: 15 total (vs 19 with resets)

## Stitching Compatibility

### Image Placement
The stitching logic works perfectly with serpentine capture because tiles are still named with (row, col) coordinates:

```python
# Tiles captured in serpentine order but placed in grid positions
for row in range(params['rows']):
    for col in range(params['cols']):
        filename = f"tile_{row}_{col}.png"
        x_offset = col * params['width']   # Grid column position
        y_offset = row * params['height']  # Grid row position
        final_image.paste(tile_image, (x_offset, y_offset))
```

### Position Verification
- **Tile (0,0)**: Always top-left (captured first)
- **Tile (2,1)**: Always bottom-middle (captured 4th in 3×3)
- **Tile (0,2)**: Always top-right (captured 7th in 3×3)

## Testing and Verification

### Test Patterns
```python
def generate_serpentine_order(rows, cols):
    order = []
    for col in range(cols):
        if col % 2 == 0:
            # Even column: top to bottom
            for row in range(rows):
                order.append((row, col))
        else:
            # Odd column: bottom to top
            for row in range(rows - 1, -1, -1):
                order.append((row, col))
    return order

# Test cases
assert generate_serpentine_order(2, 2) == [(0,0), (1,0), (1,1), (0,1)]
assert generate_serpentine_order(3, 3) == [(0,0), (1,0), (2,0), (2,1), (1,1), (0,1), (0,2), (1,2), (2,2)]
```

### Verification Points
1. **Capture Order**: Verify serpentine sequence
2. **Direction Changes**: Confirm alternating column directions
3. **No Resets**: Ensure no vertical resets between columns
4. **Efficiency**: Count total moves vs traditional method

## Troubleshooting

### Common Issues

#### Wrong Column Direction
**Symptoms**: Column captured in wrong direction (up instead of down)
**Cause**: Incorrect modulo logic for column direction
**Solution**: Verify `col % 2 == 0` for even columns (top-to-bottom)

#### Missing Column Transitions
**Symptoms**: Tiles in wrong positions after column change
**Cause**: Missing horizontal drag between columns
**Solution**: Ensure `drag_x_mm = -base_drag_amount` for column transitions

#### Incorrect Drag Direction
**Symptoms**: Map moves opposite to expected
**Cause**: Wrong drag direction for column type
**Solution**: Even columns: drag UP, Odd columns: drag DOWN

### Debug Tips
1. **Test with 2×2 grid**: Simplest serpentine pattern
2. **Check column directions**: Verify ↓↑↓↑ pattern
3. **Monitor transitions**: Ensure smooth column changes
4. **Verify final image**: Check tile placement in stitched result

## Conclusion

The serpentine traversal pattern provides optimal efficiency for grid capture by eliminating unnecessary resets and minimizing total movement. This approach is particularly beneficial for larger grids where the movement savings become more significant.

**Key Advantages**:
- ✅ **20% fewer movements** compared to traditional column-by-column
- ✅ **No vertical resets** between columns
- ✅ **Continuous flow** within each column
- ✅ **Optimal path efficiency** for any grid size
- ✅ **Perfect compatibility** with existing stitching logic
