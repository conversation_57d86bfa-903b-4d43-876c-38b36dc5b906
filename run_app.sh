#!/bin/bash

echo "Interactive Web Map Grid Screenshot & Stitcher"
echo "================================================"
echo

# Check if Python is available
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "Error: Python is not installed or not in PATH"
    echo "Please install Python 3.7+ from https://python.org"
    exit 1
fi

# Use python3 if available, otherwise python
PYTHON_CMD="python3"
if ! command -v python3 &> /dev/null; then
    PYTHON_CMD="python"
fi

echo "Testing installation..."
$PYTHON_CMD test_installation.py
if [ $? -ne 0 ]; then
    echo
    echo "Installation test failed. Please install dependencies:"
    echo "pip install -r requirements.txt"
    echo "playwright install"
    exit 1
fi

echo
echo "Starting application..."
$PYTHON_CMD main.py
