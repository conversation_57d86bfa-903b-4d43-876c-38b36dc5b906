# Interactive Web Map Grid Screenshot & Stitcher

A powerful Python GUI application that captures web map screenshots in a grid pattern and automatically stitches them together into a single high-resolution image.

## Features

- **Interactive GUI**: User-friendly tkinter interface with real-time status updates
- **Manual Positioning**: Launch browser window for precise map positioning before automated capture
- **Grid Capture**: Automatically captures screenshots in a configurable grid pattern
- **Smart Panning**: Uses JavaScript to smoothly pan the map between captures
- **Image Stitching**: Combines all tiles into a single seamless image
- **Progress Tracking**: Real-time progress updates and completion percentage
- **Error Handling**: Robust error handling with user-friendly messages
- **CSS Selector Support**: Target specific map elements for precise capture

## Requirements

- Python 3.7+
- Windows, macOS, or Linux
- Internet connection for web map access

## Installation

1. **Clone or download this project**
   ```bash
   cd "A screen capture app"
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Install Playwright browsers**
   ```bash
   playwright install
   ```

## Usage

### Quick Start

1. **Run the application**
   ```bash
   python main.py
   ```

2. **Configure parameters**
   - Enter the web map URL (e.g., OpenStreetMap, Google Maps)
   - Set capture dimensions (width/height in pixels)
   - Configure grid size (rows and columns)
   - Optionally specify a CSS selector for the map element
   - Set timing parameters for page loads and pan rendering
   - Choose output file path

3. **Launch browser**
   - Click "Launch Browser" to open the map in a new window
   - Wait for the page to load completely

4. **Position the map**
   - Manually navigate to your desired starting location
   - Position the map so your desired area is in the **CENTER** of the screen
   - Zoom to the appropriate level

5. **Start capture**
   - Return to the application window
   - Click "Start Capture" to begin automated grid capture
   - The application will automatically pan and capture each tile

6. **Wait for completion**
   - Monitor progress in the status window
   - The final stitched image will be saved to your specified location

7. **Multiple captures with dynamic settings** (NEW!)
   - After completion, the browser **stays open**
   - **Adjust any settings** (capture size, sensitivity, grid size, output path)
   - Position the map to a new area (center your next desired region)
   - Click "Start Capture" again - new settings will be applied automatically
   - Repeat as many times as needed with different settings

8. **Close browser**
   - Click "Close Browser" when finished with all captures

### Configuration Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| **Web Map URL** | URL of the web map to capture | https://hsac.org.in/eodb/ |
| **Capture Size (mm)** | Square capture area from screen center | 60.0 |
| **Drag Sensitivity Factor** | Multiplier for drag amount (3 decimal places) | 1.000 |
| **Drag Division Factor** | Split large drags into smaller movements | 3 |
| **Color Transformation** | Apply color changes (Black→White, Blue→Red, Yellow→Black) | ✓ Enabled |
| **Number of Rows** | Vertical tiles in the grid | 2 |
| **Number of Columns** | Horizontal tiles in the grid | 2 |
| **CSS Selector** | Optional selector for map element | (empty) |
| **Initial Load Wait** | Seconds to wait after page load | 10.0 |
| **Pan Render Wait** | Seconds to wait after each pan | 20.0 |
| **Output File Path** | Where to save the final image | D:/Downloaded files/kapilmoond2.png |

### CSS Selector Examples

For better targeting of map elements, you can use CSS selectors:

- **ArcGIS Maps (HSAC, Esri)**: `#map` or leave empty (uses ArcGIS API)
- **OpenStreetMap**: `#map`
- **Google Maps**: `#map`
- **Leaflet maps**: `.leaflet-container`
- **Mapbox**: `.mapboxgl-map`

**Revolutionary Mouse Drag Approach**: This application uses **realistic mouse drag simulation** instead of JavaScript API calls or CSS transforms. This approach:

- ✅ **Works with ANY web map** (ArcGIS, Google Maps, OpenStreetMap, etc.)
- ✅ **No black screens** - simulates natural user interaction
- ✅ **Reliable panning** - uses millimeter-based movements converted to pixels
- ✅ **Proper tile loading** - maps respond naturally to mouse drag events

**Serpentine Grid Traversal**: The application captures tiles in an optimized zigzag pattern:
- **Grid Order**: (0,0) → (1,0) → (2,0) → (2,1) → (1,1) → (0,1) → (0,2) → (1,2) → (2,2)
- **Pattern**: Column 1↓ Column 2↑ Column 3↓ (alternating directions)
- **Efficiency**: No vertical resets between columns, only horizontal movement
- **Dragging Logic**: DOWN for down movement, UP for up movement, LEFT for right movement
- **Perfect Stitching**: Images are automatically placed in correct grid positions

**For HSAC Digital Land Records**: Leave the CSS selector empty. The application will automatically detect the map element and perform mouse drag panning.

### Sensitivity Factor Guide

The **Drag Sensitivity Factor** allows fine-tuning of drag amounts based on map zoom level:

| Zoom Level | Recommended Sensitivity | Use Case |
|------------|------------------------|----------|
| **High Zoom** (Detailed view) | 0.300 - 0.700 | Building-level detail, street maps |
| **Medium Zoom** (Normal view) | 0.800 - 1.200 | Neighborhood-level, default usage |
| **Low Zoom** (Wide view) | 1.500 - 3.000 | City-level, regional maps |

**How to Adjust**:
- Start with **1.000** (default)
- If tiles **overlap**: Decrease sensitivity (e.g., 0.700)
- If tiles have **gaps**: Increase sensitivity (e.g., 1.300)
- Fine-tune in **0.100** increments for best results

### Dynamic Settings During Session

**NEW FEATURE**: Change settings between captures without restarting the browser!

**Changeable Settings**:
- ✅ **Capture Size**: Switch between 40mm, 60mm, 80mm, etc.
- ✅ **Sensitivity Factor**: Adjust from 0.300 to 3.000 as needed
- ✅ **Grid Size**: Change from 2x2 to 3x3, 4x4, etc.
- ✅ **Pan Wait Time**: Adjust for different map loading speeds
- ✅ **Output Path**: Save each capture to different files

**Workflow Example**:
1. Start with 60mm, sensitivity 1.000, 2x2 grid → Capture overview
2. Change to 40mm, sensitivity 0.700, 3x3 grid → Capture details
3. Change to 80mm, sensitivity 1.500, 2x2 grid → Capture wide area

### Tips for Best Results

1. **Test sensitivity first**: Capture a 2x2 grid to verify overlap/gaps
2. **Allow sufficient wait times**: 20 seconds ensures proper tile loading
3. **Center positioning**: Place desired area in screen center before capture
4. **Stable internet**: Poor connections may cause incomplete tile loading
5. **Consistent zoom**: Don't change zoom during manual positioning
6. **Dynamic adjustments**: Fine-tune settings between captures for optimal results

## Troubleshooting

### Common Issues

**"Missing Dependencies" error**
- Run `pip install -r requirements.txt`
- Run `playwright install`

**Browser doesn't launch**
- Check if Playwright browsers are installed
- Try running `playwright install chromium`

**Map doesn't pan correctly**
- Verify the CSS selector is correct
- Some maps may use different panning mechanisms
- Try increasing the pan render wait time

**Tiles appear incomplete**
- Increase the "Pan Render Wait" time
- Check internet connection stability
- Some maps load tiles progressively

**Stitched image has gaps**
- Ensure capture dimensions match the actual rendered area
- Some maps have UI elements that affect capture area

### Getting Help

If you encounter issues:

1. Check the status messages in the application
2. Verify all parameters are correct
3. Test with a simple 2x2 grid first
4. Try different CSS selectors or leave it empty

## Technical Details

### Architecture

- **GUI Framework**: tkinter with ttk styling
- **Browser Automation**: Playwright (Chromium)
- **Image Processing**: Pillow (PIL)
- **Threading**: Background automation with thread-safe communication
- **File Management**: Temporary file handling with automatic cleanup

### Workflow

1. **Initialization**: GUI setup and parameter validation
2. **Browser Launch**: Non-headless Chromium with maximized window
3. **Manual Positioning**: User positions map to desired starting point
4. **Automated Capture**: JavaScript-based panning and screenshot capture
5. **Image Stitching**: Pillow-based tile combination
6. **Cleanup**: Temporary file removal and browser closure

### Performance

- **Memory Usage**: Optimized for large grids with streaming image processing
- **Speed**: Depends on map loading times and network speed
- **Quality**: Maintains original image quality throughout the process

## License

This project is created for educational and personal use. Please respect the terms of service of the web maps you capture.

## Author

Created by Moond Sahab

---

**Note**: Always ensure you have permission to capture screenshots from web services and respect their terms of service and rate limits.
