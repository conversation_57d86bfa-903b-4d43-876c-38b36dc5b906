@echo off
echo Interactive Web Map Grid Screenshot ^& Stitcher
echo ================================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

echo Testing installation...
python test_installation.py
if errorlevel 1 (
    echo.
    echo Installation test failed. Please install dependencies:
    echo pip install -r requirements.txt
    echo playwright install
    pause
    exit /b 1
)

echo.
echo Starting application...
python main.py

pause
