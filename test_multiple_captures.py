"""
Test the multiple capture functionality (browser stays open)
"""
import tkinter as tk
from tkinter import messagebox
import time
import threading
import queue
from screenshot_automation import ScreenshotAutomation


def test_multiple_captures():
    """Test that browser stays open for multiple captures"""
    
    print("Testing Multiple Capture Functionality")
    print("=" * 60)
    
    # Create test parameters
    params = {
        'url': 'https://hsac.org.in/eodb/',
        'capture_size_mm': 60.0,
        'sensitivity': 1.000,
        'width': 227,  # 60mm at 96 DPI
        'height': 227,
        'rows': 2,
        'cols': 2,
        'selector': '',
        'initial_wait': 10.0,
        'pan_wait': 20.0,
        'output_path': 'test_multiple_capture_1.png'
    }
    
    # Create communication objects
    status_queue = queue.Queue()
    start_event = threading.Event()
    
    # Create automation instance
    automation = ScreenshotAutomation(status_queue, start_event)
    
    print("1. Starting first automation (browser launch)...")
    
    # Start automation thread
    automation_thread = threading.Thread(
        target=automation.run_automation,
        args=(params,),
        daemon=True
    )
    automation_thread.start()
    
    # Monitor first capture
    browser_ready = False
    first_capture_complete = False
    
    print("2. Monitoring first capture process...")
    
    while automation_thread.is_alive() or not status_queue.empty():
        try:
            message_type, content, progress = status_queue.get(timeout=1)
            
            if message_type == 'status':
                progress_str = f" ({progress:.1f}%)" if progress is not None else ""
                print(f"   STATUS{progress_str}: {content}")
                
            elif message_type == 'signal':
                print(f"   SIGNAL: {content}")
                
                if content == "BROWSER_READY":
                    browser_ready = True
                    print("\n" + "="*50)
                    print("BROWSER IS READY FOR FIRST CAPTURE")
                    print("="*50)
                    print("Simulating user positioning and starting capture...")
                    time.sleep(2)  # Simulate user positioning time
                    start_event.set()
                    
                elif content == "PROCESS_COMPLETE":
                    first_capture_complete = True
                    print("\n" + "="*50)
                    print("FIRST CAPTURE COMPLETED")
                    print("="*50)
                    break
                    
                elif content == "ERROR":
                    print("ERROR: First capture failed!")
                    return False
                    
        except queue.Empty:
            continue
    
    if not first_capture_complete:
        print("✗ First capture did not complete successfully")
        return False
    
    print("✓ First capture completed successfully")
    
    # Test that browser is still open
    print("\n3. Testing browser state after first capture...")
    
    try:
        if automation.browser and automation.page:
            # Try to interact with the page
            page_title = automation.page.title()
            print(f"✓ Browser still open - Page title: {page_title}")
            
            # Test that we can still navigate
            current_url = automation.page.url
            print(f"✓ Page accessible - URL: {current_url}")
            
        else:
            print("✗ Browser or page is None - browser was closed")
            return False
            
    except Exception as e:
        print(f"✗ Error accessing browser: {str(e)}")
        return False
    
    # Simulate second capture
    print("\n4. Starting second capture (same browser session)...")
    
    # Update output path for second capture
    params['output_path'] = 'test_multiple_capture_2.png'
    
    # Clear the start event for second capture
    start_event.clear()
    
    # Start second capture (should reuse existing browser)
    print("   Simulating user repositioning map...")
    time.sleep(3)  # Simulate user repositioning
    
    print("   Starting second capture...")
    start_event.set()
    
    # Monitor second capture
    second_capture_complete = False
    
    # Continue monitoring the same automation instance
    timeout_counter = 0
    max_timeout = 120  # 2 minutes timeout
    
    while timeout_counter < max_timeout:
        try:
            message_type, content, progress = status_queue.get(timeout=1)
            
            if message_type == 'status':
                progress_str = f" ({progress:.1f}%)" if progress is not None else ""
                print(f"   STATUS{progress_str}: {content}")
                
            elif message_type == 'signal':
                print(f"   SIGNAL: {content}")
                
                if content == "PROCESS_COMPLETE":
                    second_capture_complete = True
                    print("\n" + "="*50)
                    print("SECOND CAPTURE COMPLETED")
                    print("="*50)
                    break
                    
                elif content == "ERROR":
                    print("ERROR: Second capture failed!")
                    break
                    
        except queue.Empty:
            timeout_counter += 1
            continue
    
    # Test results
    print("\n" + "="*60)
    print("MULTIPLE CAPTURE TEST RESULTS")
    print("="*60)
    
    results = {
        "Browser Launch": browser_ready,
        "First Capture": first_capture_complete,
        "Browser Stays Open": automation.browser is not None,
        "Second Capture": second_capture_complete
    }
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name:<20} {status}")
    
    all_passed = all(results.values())
    
    print(f"\nOverall Result: {'✓ ALL TESTS PASSED' if all_passed else '✗ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n✓ Multiple capture functionality working correctly!")
        print("✓ Browser stays open between captures")
        print("✓ Users can perform multiple grid captures without reloading")
    else:
        print("\n✗ Multiple capture functionality has issues")
    
    # Cleanup
    print("\n5. Cleaning up test...")
    try:
        if automation:
            automation._cleanup_all()
        print("✓ Cleanup completed")
    except Exception as e:
        print(f"⚠ Cleanup error: {str(e)}")
    
    return all_passed


if __name__ == "__main__":
    success = test_multiple_captures()
    print(f"\nTest {'PASSED' if success else 'FAILED'}")
