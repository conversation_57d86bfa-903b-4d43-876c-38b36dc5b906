"""
Test script to verify screenshot capture functionality with HSAC website
"""
from screenshot_automation import ScreenshotAutomation
import threading
import queue
import time
import os


def test_hsac_capture():
    """Test the screenshot automation with HSAC website"""
    
    print("Testing Screenshot Automation with HSAC Website")
    print("=" * 60)
    
    # Create communication objects
    status_queue = queue.Queue()
    start_event = threading.Event()
    
    # Test parameters
    params = {
        'url': 'https://hsac.org.in/eodb/',
        'width': 400,  # Smaller for testing
        'height': 300,
        'rows': 2,
        'cols': 2,
        'selector': '',  # Empty for ArcGIS auto-detection
        'initial_wait': 10.0,
        'pan_wait': 4.0,
        'output_path': 'test_hsac_capture.png'
    }
    
    print(f"Test parameters:")
    print(f"  URL: {params['url']}")
    print(f"  Grid: {params['rows']}x{params['cols']}")
    print(f"  Tile size: {params['width']}x{params['height']}")
    print(f"  Output: {params['output_path']}")
    print()
    
    # Create automation instance
    automation = ScreenshotAutomation(status_queue, start_event)
    
    # Start automation in background thread
    automation_thread = threading.Thread(
        target=automation.run_automation,
        args=(params,),
        daemon=True
    )
    
    print("Starting automation thread...")
    automation_thread.start()
    
    # Monitor status messages
    browser_ready = False
    process_complete = False
    
    print("Monitoring automation progress...")
    print("-" * 40)
    
    while automation_thread.is_alive() or not status_queue.empty():
        try:
            message_type, content, progress = status_queue.get(timeout=1)
            
            if message_type == 'status':
                progress_str = f" ({progress:.1f}%)" if progress is not None else ""
                print(f"STATUS{progress_str}: {content}")
                
            elif message_type == 'signal':
                print(f"SIGNAL: {content}")
                
                if content == "BROWSER_READY":
                    browser_ready = True
                    print("\n" + "="*40)
                    print("BROWSER IS READY!")
                    print("Please manually position the map in the browser window")
                    print("to show the area you want to capture, then press Enter")
                    print("to continue with automated capture...")
                    print("="*40)
                    
                    # Wait for user input
                    input("Press Enter when map is positioned correctly: ")
                    
                    print("Starting automated capture...")
                    start_event.set()
                    
                elif content == "PROCESS_COMPLETE":
                    process_complete = True
                    break
                    
                elif content == "ERROR":
                    print("ERROR: Process failed!")
                    break
                    
        except queue.Empty:
            continue
    
    # Wait for thread to complete
    automation_thread.join(timeout=5)
    
    print("\n" + "="*60)
    print("TEST RESULTS")
    print("="*60)
    
    if process_complete:
        print("✓ Automation completed successfully!")
        
        # Check if output file exists
        if os.path.exists(params['output_path']):
            file_size = os.path.getsize(params['output_path'])
            print(f"✓ Output file created: {params['output_path']} ({file_size} bytes)")
            
            # Try to get image dimensions
            try:
                from PIL import Image
                with Image.open(params['output_path']) as img:
                    print(f"✓ Image dimensions: {img.size[0]}x{img.size[1]} pixels")
                    expected_width = params['cols'] * params['width']
                    expected_height = params['rows'] * params['height']
                    print(f"  Expected: {expected_width}x{expected_height} pixels")
                    
                    if img.size == (expected_width, expected_height):
                        print("✓ Image dimensions match expected grid size!")
                    else:
                        print("⚠ Image dimensions don't match expected size")
                        
            except Exception as e:
                print(f"⚠ Could not analyze image: {e}")
        else:
            print(f"✗ Output file not found: {params['output_path']}")
    else:
        print("✗ Automation did not complete successfully")
    
    print(f"\nBrowser ready: {browser_ready}")
    print(f"Process complete: {process_complete}")
    
    if os.path.exists(params['output_path']):
        print(f"\nYou can view the captured image at: {params['output_path']}")


if __name__ == "__main__":
    test_hsac_capture()
