"""
Test script to verify installation and dependencies
"""
import sys
import importlib


def test_python_version():
    """Test Python version compatibility"""
    print("Testing Python version...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 7:
        print(f"✓ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    else:
        print(f"✗ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.7+")
        return False


def test_dependency(module_name, import_name=None, description=""):
    """Test if a dependency is available"""
    if import_name is None:
        import_name = module_name
    
    try:
        module = importlib.import_module(import_name)
        version = getattr(module, '__version__', 'unknown')
        print(f"✓ {module_name} {version} - {description}")
        return True
    except ImportError:
        print(f"✗ {module_name} - Missing ({description})")
        return False


def test_tkinter():
    """Test tkinter availability"""
    try:
        import tkinter as tk
        from tkinter import ttk
        print("✓ tkinter - GUI framework available")
        return True
    except ImportError:
        print("✗ tkinter - GUI framework missing")
        return False


def test_playwright_browsers():
    """Test if Playwright browsers are installed"""
    try:
        from playwright.sync_api import sync_playwright
        with sync_playwright() as p:
            # Try to get browser executable path
            browser_path = p.chromium.executable_path
            if browser_path:
                print("✓ Playwright browsers - Chromium available")
                return True
            else:
                print("✗ Playwright browsers - Chromium not found")
                return False
    except Exception as e:
        print(f"✗ Playwright browsers - Error: {str(e)}")
        return False


def main():
    """Run all tests"""
    print("=" * 60)
    print("Interactive Web Map Grid Screenshot & Stitcher")
    print("Installation Test")
    print("=" * 60)
    print()
    
    tests = [
        ("Python Version", test_python_version),
        ("tkinter", test_tkinter),
        ("Playwright", lambda: test_dependency("playwright", "playwright", "Browser automation")),
        ("Pillow", lambda: test_dependency("Pillow", "PIL", "Image processing")),
        ("Playwright Browsers", test_playwright_browsers),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} - Error during test: {str(e)}")
            results.append((test_name, False))
        print()
    
    # Summary
    print("=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<25} {status}")
    
    print()
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed! The application should work correctly.")
        print("\nYou can now run the application with:")
        print("python main.py")
    else:
        print("✗ Some tests failed. Please install missing dependencies:")
        print("\n1. Install Python packages:")
        print("   pip install -r requirements.txt")
        print("\n2. Install Playwright browsers:")
        print("   playwright install")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
