# Sensitivity Factor Guide

## Overview

The **Drag Sensitivity Factor** is a precision control feature that allows users to fine-tune the mouse drag amount for optimal grid capture results. This feature multiplies the base drag distance by a user-defined factor (up to 3 decimal places).

## How It Works

### Base Calculation
- **Base Drag Amount** = Capture Size (mm)
- **Actual Drag Amount** = Base Drag Amount × Sensitivity Factor
- **Example**: 60mm × 1.500 = 90mm actual drag

### Precision Control
- **Input Range**: 0.001 - 10.000
- **Decimal Places**: Up to 3 (e.g., 1.234)
- **Default Value**: 1.000 (no adjustment)

## Sensitivity Factor Usage Guide

### By Zoom Level

| Zoom Level | Sensitivity Range | Description | Example Use |
|------------|------------------|-------------|-------------|
| **High Zoom** | 0.300 - 0.700 | Detailed street-level view | Building layouts, property boundaries |
| **Medium Zoom** | 0.800 - 1.200 | Neighborhood-level view | District mapping, area surveys |
| **Low Zoom** | 1.500 - 3.000 | City/regional view | Large area coverage, regional planning |

### By Map Type

| Map Type | Recommended Sensitivity | Notes |
|----------|------------------------|-------|
| **HSAC Land Records** | 0.800 - 1.200 | Depends on zoom level |
| **Google Maps** | 0.600 - 1.000 | Generally needs lower values |
| **OpenStreetMap** | 1.000 - 1.500 | Standard sensitivity works well |
| **Satellite Imagery** | 0.500 - 0.800 | High detail requires precision |

## Adjustment Process

### Step 1: Initial Test
1. Set sensitivity to **1.000** (default)
2. Capture a **2×2 grid** as test
3. Examine results for overlap or gaps

### Step 2: Fine-Tuning
- **If tiles overlap**: Decrease sensitivity by 0.100-0.200
- **If tiles have gaps**: Increase sensitivity by 0.100-0.200
- **If perfect**: Keep current value

### Step 3: Precision Adjustment
- Fine-tune in **0.050** increments
- For critical work, use **0.010** increments
- Test with larger grids (3×3 or 4×4)

## Common Sensitivity Values

### Frequently Used Settings
- **0.500**: Half movement (high zoom, detailed work)
- **0.750**: Three-quarter movement (medium-high zoom)
- **1.000**: Normal movement (default, medium zoom)
- **1.250**: Quarter extra movement (medium-low zoom)
- **1.500**: Half extra movement (low zoom, wide areas)
- **2.000**: Double movement (very low zoom, large regions)

### Special Cases
- **0.300**: Ultra-precise (maximum zoom, building details)
- **0.100**: Micro-adjustments (testing purposes)
- **3.000**: Maximum coverage (continental-scale mapping)

## Technical Implementation

### Calculation Formula
```
Actual Drag (mm) = Base Capture Size (mm) × Sensitivity Factor
Actual Drag (px) = Actual Drag (mm) × 3.78 (pixels per mm at 96 DPI)
```

### Example Calculations
| Base Size | Sensitivity | Actual Drag | Pixels |
|-----------|-------------|-------------|--------|
| 60mm | 0.500 | 30mm | 113px |
| 60mm | 1.000 | 60mm | 227px |
| 60mm | 1.500 | 90mm | 340px |
| 60mm | 2.000 | 120mm | 454px |

## Troubleshooting

### Problem: Tiles Overlap
**Symptoms**: Adjacent tiles show same content
**Solution**: Decrease sensitivity factor
**Adjustment**: Reduce by 0.100-0.200

### Problem: Gaps Between Tiles
**Symptoms**: Missing areas between tiles
**Solution**: Increase sensitivity factor
**Adjustment**: Increase by 0.100-0.200

### Problem: Inconsistent Results
**Symptoms**: Some tiles overlap, others have gaps
**Solutions**:
1. Check internet connection stability
2. Increase pan render wait time
3. Ensure map zoom doesn't change during capture

### Problem: Extreme Values Needed
**Symptoms**: Need sensitivity > 3.000 or < 0.100
**Solutions**:
1. Adjust capture size instead of sensitivity
2. Change map zoom level
3. Check if map responds to mouse drag

## Best Practices

### Before Starting
1. **Test with small grid** (2×2) first
2. **Document working values** for different zoom levels
3. **Ensure stable internet** connection
4. **Close unnecessary applications** for smooth operation

### During Capture
1. **Don't change zoom** after positioning
2. **Don't interact** with browser during automation
3. **Monitor progress** through status messages
4. **Be patient** with render wait times

### After Capture
1. **Examine tile boundaries** for quality
2. **Note successful sensitivity values** for future use
3. **Adjust and re-capture** if needed
4. **Save settings** for similar projects

## Advanced Usage

### Fractional Adjustments
- **1.050**: 5% increase (fine-tuning)
- **0.975**: 2.5% decrease (precision work)
- **1.125**: 12.5% increase (specific corrections)

### Zoom-Specific Profiles
Create sensitivity profiles for different zoom levels:
- **Profile 1**: High zoom (0.600)
- **Profile 2**: Medium zoom (1.000)
- **Profile 3**: Low zoom (1.800)

### Quality Control
- Always capture **overlap samples** at tile boundaries
- Use **consistent lighting** conditions for satellite imagery
- **Verify coordinates** for georeferenced work

## Conclusion

The Sensitivity Factor provides precise control over grid capture quality, enabling perfect tile alignment across different map types and zoom levels. Start with the default value of 1.000 and adjust based on your specific requirements and map characteristics.

**Remember**: Small adjustments (0.050-0.100) often yield the best results. Patience and testing are key to achieving perfect grid captures.
