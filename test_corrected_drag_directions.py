"""
Test the corrected up/down drag directions
"""
from playwright.sync_api import sync_playwright
import time
import os


def test_corrected_drag_directions():
    """Test corrected drag directions: DOWN to show bottom, UP to show top"""
    
    print("Testing Corrected Up/Down Drag Directions")
    print("=" * 60)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False, args=["--start-maximized"])
        page = browser.new_page()
        
        print("Navigating to HSAC website...")
        page.goto("https://hsac.org.in/eodb/")
        
        print("Waiting 10 seconds for map to load...")
        time.sleep(10)
        
        # Test parameters
        capture_size_mm = 60.0
        sensitivity = 1.000
        pixels_per_mm = 96 / 25.4  # ~3.78 pixels per mm
        capture_size_px = int(capture_size_mm * pixels_per_mm)  # ~227 pixels
        
        print(f"Test parameters: {capture_size_mm}mm capture, sensitivity {sensitivity:.3f}")
        
        def capture_center_area(filename, description):
            """Capture the center area of the screen"""
            viewport_size = page.viewport_size
            center_x = viewport_size['width'] // 2
            center_y = viewport_size['height'] // 2
            
            capture_x = center_x - (capture_size_px // 2)
            capture_y = center_y - (capture_size_px // 2)
            
            # Ensure bounds
            capture_x = max(0, min(capture_x, viewport_size['width'] - capture_size_px))
            capture_y = max(0, min(capture_y, viewport_size['height'] - capture_size_px))
            
            page.screenshot(
                path=filename,
                clip={
                    'x': capture_x,
                    'y': capture_y,
                    'width': capture_size_px,
                    'height': capture_size_px
                }
            )
            print(f"✓ {description}: {filename}")
        
        def perform_drag(drag_x_mm, drag_y_mm, description):
            """Perform mouse drag with given mm amounts"""
            print(f"\n{description}")
            print(f"  Dragging: {drag_x_mm:.1f}mm x {drag_y_mm:.1f}mm")
            
            try:
                # Find map element
                map_element = page.locator('#map').first
                if map_element.is_visible():
                    bbox = map_element.bounding_box()
                    drag_center_x = bbox['x'] + bbox['width'] // 2
                    drag_center_y = bbox['y'] + bbox['height'] // 2
                else:
                    viewport = page.viewport_size
                    drag_center_x = viewport['width'] // 2
                    drag_center_y = viewport['height'] // 2
                
                # Convert mm to pixels
                delta_x_px = int(drag_x_mm * pixels_per_mm)
                delta_y_px = int(drag_y_mm * pixels_per_mm)
                
                print(f"  Pixel movement: {delta_x_px}px x {delta_y_px}px")
                
                # Perform drag
                page.mouse.move(drag_center_x, drag_center_y)
                page.mouse.down()
                page.mouse.move(
                    drag_center_x + delta_x_px,
                    drag_center_y + delta_y_px,
                    steps=15
                )
                page.mouse.up()
                
                print(f"  ✓ Drag completed")
                return True
                
            except Exception as e:
                print(f"  ✗ Drag failed: {str(e)}")
                return False
        
        # Test corrected drag directions
        print("\n" + "="*60)
        print("TESTING CORRECTED DRAG DIRECTIONS")
        print("="*60)
        
        base_drag_amount = capture_size_mm * sensitivity
        
        # Test 1: Initial capture
        print("\n1. Capturing initial center position...")
        capture_center_area("corrected_initial.png", "Initial center position")
        
        # Test 2: Drag DOWN to show BOTTOM area (corrected)
        print("\n2. Testing: Drag DOWN to show BOTTOM area")
        print("   CORRECTED: Negative Y = drag down, should show bottom portion")
        if perform_drag(0, -base_drag_amount, "Drag DOWN to show bottom area"):
            time.sleep(4)
            capture_center_area("corrected_bottom.png", "Bottom area (after drag down)")
        
        # Test 3: Drag UP to show TOP area (corrected)  
        print("\n3. Testing: Drag UP to show TOP area")
        print("   CORRECTED: Positive Y = drag up, should show top portion")
        if perform_drag(0, base_drag_amount, "Drag UP to show top area"):
            time.sleep(4)
            capture_center_area("corrected_top.png", "Top area (after drag up)")
        
        # Test 4: Drag LEFT to show RIGHT area (already correct)
        print("\n4. Testing: Drag LEFT to show RIGHT area")
        print("   ALREADY CORRECT: Negative X = drag left, should show right portion")
        if perform_drag(-base_drag_amount, 0, "Drag LEFT to show right area"):
            time.sleep(4)
            capture_center_area("corrected_right.png", "Right area (after drag left)")
        
        # Test 5: Drag RIGHT to show LEFT area (for completeness)
        print("\n5. Testing: Drag RIGHT to show LEFT area")
        print("   FOR COMPLETENESS: Positive X = drag right, should show left portion")
        if perform_drag(base_drag_amount, 0, "Drag RIGHT to show left area"):
            time.sleep(4)
            capture_center_area("corrected_left.png", "Left area (after drag right)")
        
        # Verify results
        print("\n" + "="*60)
        print("CORRECTED DRAG DIRECTION VERIFICATION")
        print("="*60)
        
        test_files = [
            "corrected_initial.png",
            "corrected_bottom.png",
            "corrected_top.png", 
            "corrected_right.png",
            "corrected_left.png"
        ]
        
        print("Captured files:")
        success_count = 0
        for filename in test_files:
            if os.path.exists(filename):
                size = os.path.getsize(filename)
                print(f"  ✓ {filename}: {size} bytes")
                success_count += 1
            else:
                print(f"  ✗ {filename}: Missing")
        
        print(f"\nCapture success rate: {success_count}/{len(test_files)}")
        
        # Test corrected serpentine logic
        print("\n" + "="*60)
        print("CORRECTED SERPENTINE LOGIC SUMMARY")
        print("="*60)
        
        print("Corrected Drag Directions:")
        print("✓ Show BOTTOM area → Drag DOWN (negative Y)")
        print("✓ Show TOP area → Drag UP (positive Y)")
        print("✓ Show RIGHT area → Drag LEFT (negative X)")
        print("✓ Show LEFT area → Drag RIGHT (positive X)")
        
        print("\nSerpentine Pattern with Corrected Directions:")
        print("Column 1 (↓): (0,0) → drag DOWN → (1,0) → drag DOWN → (2,0)")
        print("Column 2 (↑): drag LEFT → (2,1) → drag UP → (1,1) → drag UP → (0,1)")
        print("Column 3 (↓): drag LEFT → (0,2) → drag DOWN → (1,2) → drag DOWN → (2,2)")
        
        if success_count == len(test_files):
            print("\n✓ All drag direction tests successful!")
            print("✓ Corrected up/down directions working properly")
            print("✓ Drag DOWN shows bottom, drag UP shows top")
            print("✓ Left/right directions remain correct")
        else:
            print("\n⚠ Some drag direction tests failed")
        
        print("\nTest complete. Browser will close in 10 seconds...")
        time.sleep(10)
        
        browser.close()
        
        return success_count == len(test_files)


if __name__ == "__main__":
    success = test_corrected_drag_directions()
    print(f"\nCorrected Drag Directions Test: {'PASSED' if success else 'FAILED'}")
