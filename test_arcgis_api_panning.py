"""
Test the new ArcGIS API panning approach with HSAC website
"""
from playwright.sync_api import sync_playwright
import time
import os


def test_arcgis_api_panning():
    """Test the ArcGIS API panning approach"""
    
    print("Testing ArcGIS API Panning for HSAC Website")
    print("=" * 60)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False, args=["--start-maximized"])
        page = browser.new_page()
        
        print("Navigating to HSAC website...")
        page.goto("https://hsac.org.in/eodb/")
        
        print("Waiting for ArcGIS map to load...")
        time.sleep(12)  # Give extra time for ArcGIS to fully load
        
        # Test our new ArcGIS API panning JavaScript
        test_pan_js = '''
        (() => {
            const deltaX = -100;
            const deltaY = -50;
            
            // Find the MapView instance - common variable names
            let mapView = window.view || window.map || window.mapView;
            
            // If not found globally, try to find it attached to the map element
            if (!mapView) {
                const mapElement = document.querySelector('#map');
                if (mapElement) {
                    mapView = mapElement.view || mapElement.map || mapElement._view;
                }
            }
            
            // Try to find in common ArcGIS global objects
            if (!mapView && window.require) {
                if (window.esriView) mapView = window.esriView;
                if (window.myView) mapView = window.myView;
                if (window.mainView) mapView = window.mainView;
            }
            
            if (!mapView) {
                return {
                    success: false,
                    error: 'ArcGIS MapView not found',
                    debug: {
                        'window.view': typeof window.view,
                        'window.map': typeof window.map,
                        'window.mapView': typeof window.mapView,
                        'window.require': typeof window.require,
                        'mapElement': !!document.querySelector('#map')
                    }
                };
            }
            
            if (!mapView.ready) {
                return {
                    success: false,
                    error: 'ArcGIS MapView not ready',
                    ready: mapView.ready
                };
            }
            
            // Get current view properties
            const currentCenter = mapView.center;
            const currentExtent = mapView.extent;
            
            // Calculate map units per pixel for coordinate conversion
            const mapUnitsPerPixelX = currentExtent.width / mapView.width;
            const mapUnitsPerPixelY = currentExtent.height / mapView.height;
            
            // Calculate offset in map units
            const offsetX = deltaX * mapUnitsPerPixelX;
            const offsetY = deltaY * mapUnitsPerPixelY;
            
            // Get current coordinates (handle both geographic and projected)
            let currentX, currentY;
            const spatialRef = mapView.spatialReference;
            const isWebMercator = spatialRef && (spatialRef.wkid === 102100 || spatialRef.wkid === 3857);
            
            if (isWebMercator || (currentCenter.x !== undefined && currentCenter.y !== undefined)) {
                // Use x,y for projected coordinates
                currentX = currentCenter.x;
                currentY = currentCenter.y;
            } else {
                // Use longitude/latitude for geographic coordinates
                currentX = currentCenter.longitude;
                currentY = currentCenter.latitude;
            }
            
            // Calculate new center coordinates
            const newCenterX = currentX + offsetX;
            const newCenterY = currentY - offsetY; // Negative because screen Y increases downward
            
            // Pan to new location using ArcGIS API
            mapView.goTo({
                center: [newCenterX, newCenterY]
            }, {
                animate: false,
                duration: 0
            });
            
            return {
                success: true,
                method: 'arcgis_api',
                spatialReference: spatialRef ? spatialRef.wkid : 'unknown',
                oldCenter: [currentX, currentY],
                newCenter: [newCenterX, newCenterY],
                offsetX: offsetX,
                offsetY: offsetY,
                mapUnitsPerPixelX: mapUnitsPerPixelX,
                mapUnitsPerPixelY: mapUnitsPerPixelY,
                viewSize: [mapView.width, mapView.height],
                extentSize: [currentExtent.width, currentExtent.height]
            };
        })()
        '''
        
        print("Testing ArcGIS MapView detection...")
        result = page.evaluate(test_pan_js)
        
        print("\nResults:")
        print(f"Success: {result.get('success')}")
        
        if result.get('success'):
            print(f"✓ ArcGIS API panning successful!")
            print(f"Method: {result.get('method')}")
            print(f"Spatial Reference: {result.get('spatialReference')}")
            print(f"Old Center: {result.get('oldCenter')}")
            print(f"New Center: {result.get('newCenter')}")
            print(f"Offset: ({result.get('offsetX'):.2f}, {result.get('offsetY'):.2f})")
            print(f"Map Units/Pixel: {result.get('mapUnitsPerPixelX'):.2f}")
            print(f"View Size: {result.get('viewSize')}")
            print(f"Extent Size: {result.get('extentSize')}")
            
            # Take screenshots to verify the pan worked
            print("\nTaking screenshots to verify panning...")
            
            print("Taking initial screenshot...")
            page.screenshot(path="arcgis_initial.png", clip={'x': 400, 'y': 250, 'width': 400, 'height': 300})
            
            print("Waiting for map to update...")
            time.sleep(4)
            
            # Check if map is still updating
            updating = page.evaluate("window.view && window.view.updating")
            print(f"Map updating: {updating}")
            
            if updating:
                print("Waiting for map to finish updating...")
                for i in range(10):
                    time.sleep(1)
                    updating = page.evaluate("window.view && window.view.updating")
                    if not updating:
                        break
                print(f"Map finished updating: {not updating}")
            
            print("Taking post-pan screenshot...")
            page.screenshot(path="arcgis_after_pan.png", clip={'x': 400, 'y': 250, 'width': 400, 'height': 300})
            
            # Test a second pan
            print("Testing second pan...")
            result2 = page.evaluate(test_pan_js)
            if result2.get('success'):
                time.sleep(4)
                page.screenshot(path="arcgis_second_pan.png", clip={'x': 400, 'y': 250, 'width': 400, 'height': 300})
                print("✓ Second pan successful")
            
        else:
            print(f"✗ ArcGIS API panning failed!")
            print(f"Error: {result.get('error')}")
            if result.get('debug'):
                print("Debug information:")
                for key, value in result.get('debug', {}).items():
                    print(f"  {key}: {value}")
            
            print("\nTrying to get more debug info...")
            debug_info = page.evaluate("""
                ({
                    windowKeys: Object.keys(window).filter(k => k.includes('view') || k.includes('map')),
                    mapElement: !!document.querySelector('#map'),
                    esriElements: document.querySelectorAll('[class*="esri"]').length,
                    requireExists: typeof window.require,
                    dojoExists: typeof window.dojo
                })
            """)
            print("Additional debug info:")
            for key, value in debug_info.items():
                print(f"  {key}: {value}")
        
        print("\nScreenshot files created:")
        for filename in ["arcgis_initial.png", "arcgis_after_pan.png", "arcgis_second_pan.png"]:
            if os.path.exists(filename):
                size = os.path.getsize(filename)
                print(f"  {filename}: {size} bytes")
        
        print("\nTest complete. Browser will close in 10 seconds...")
        print("You can manually inspect the map behavior during this time.")
        time.sleep(10)
        
        browser.close()


if __name__ == "__main__":
    test_arcgis_api_panning()
