"""
Test the column-by-column grid traversal logic
"""
from playwright.sync_api import sync_playwright
import time
import os


def test_column_traversal():
    """Test column-by-column grid traversal with correct dragging"""
    
    print("Testing Column-by-Column Grid Traversal")
    print("=" * 60)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False, args=["--start-maximized"])
        page = browser.new_page()
        
        print("Navigating to HSAC website...")
        page.goto("https://hsac.org.in/eodb/")
        
        print("Waiting 10 seconds for map to load...")
        time.sleep(10)
        
        # Test parameters
        capture_size_mm = 60.0
        sensitivity = 1.000
        pixels_per_mm = 96 / 25.4  # ~3.78 pixels per mm
        capture_size_px = int(capture_size_mm * pixels_per_mm)  # ~227 pixels
        
        print(f"Test parameters: {capture_size_mm}mm capture, sensitivity {sensitivity:.3f}")
        
        def capture_center_area(filename, description):
            """Capture the center area of the screen"""
            viewport_size = page.viewport_size
            center_x = viewport_size['width'] // 2
            center_y = viewport_size['height'] // 2
            
            capture_x = center_x - (capture_size_px // 2)
            capture_y = center_y - (capture_size_px // 2)
            
            # Ensure bounds
            capture_x = max(0, min(capture_x, viewport_size['width'] - capture_size_px))
            capture_y = max(0, min(capture_y, viewport_size['height'] - capture_size_px))
            
            page.screenshot(
                path=filename,
                clip={
                    'x': capture_x,
                    'y': capture_y,
                    'width': capture_size_px,
                    'height': capture_size_px
                }
            )
            print(f"✓ {description}: {filename}")
        
        def perform_drag(drag_x_mm, drag_y_mm, description):
            """Perform mouse drag with given mm amounts"""
            print(f"\n{description}")
            print(f"  Dragging: {drag_x_mm:.1f}mm x {drag_y_mm:.1f}mm")
            
            try:
                # Find map element
                map_element = page.locator('#map').first
                if map_element.is_visible():
                    bbox = map_element.bounding_box()
                    drag_center_x = bbox['x'] + bbox['width'] // 2
                    drag_center_y = bbox['y'] + bbox['height'] // 2
                else:
                    viewport = page.viewport_size
                    drag_center_x = viewport['width'] // 2
                    drag_center_y = viewport['height'] // 2
                
                # Convert mm to pixels
                delta_x_px = int(drag_x_mm * pixels_per_mm)
                delta_y_px = int(drag_y_mm * pixels_per_mm)
                
                print(f"  Pixel movement: {delta_x_px}px x {delta_y_px}px")
                
                # Perform drag
                page.mouse.move(drag_center_x, drag_center_y)
                page.mouse.down()
                page.mouse.move(
                    drag_center_x + delta_x_px,
                    drag_center_y + delta_y_px,
                    steps=15
                )
                page.mouse.up()
                
                print(f"  ✓ Drag completed")
                return True
                
            except Exception as e:
                print(f"  ✗ Drag failed: {str(e)}")
                return False
        
        # Test 3x3 grid column-by-column traversal
        print("\n" + "="*60)
        print("TESTING 3x3 GRID COLUMN-BY-COLUMN TRAVERSAL")
        print("="*60)
        print("Order: (0,0) → (1,0) → (2,0) → (0,1) → (1,1) → (2,1) → (0,2) → (1,2) → (2,2)")
        
        rows = 3
        cols = 3
        base_drag_amount = capture_size_mm * sensitivity
        
        # Capture sequence following column-by-column logic
        for col in range(cols):
            for row in range(rows):
                if row == 0 and col == 0:
                    # Initial capture (0,0)
                    print(f"\n1. Capturing initial tile (0,0) - Column 1, Row 1")
                    capture_center_area(f"col_tile_{row}_{col}.png", f"Tile ({row},{col})")
                    continue
                
                # Calculate drag direction
                if row == 0 and col > 0:
                    # Moving to next column (starting from top)
                    # First, reset to top position by dragging DOWN
                    # Then drag LEFT to reveal area to the right
                    reset_drag_y_mm = (rows - 1) * base_drag_amount  # Drag down to reset to top
                    drag_x_mm = -base_drag_amount  # Drag left to show right area
                    
                    print(f"\n{col*rows + row + 1}. Moving to Column {col+1} (tile {row},{col})")
                    
                    # First drag: reset to top
                    if reset_drag_y_mm > 0:
                        if perform_drag(0, reset_drag_y_mm, f"Reset to top: {reset_drag_y_mm:.1f}mm down"):
                            time.sleep(4)
                    
                    # Second drag: move to next column
                    if perform_drag(drag_x_mm, 0, f"Move to column {col+1}: {abs(drag_x_mm):.1f}mm left"):
                        time.sleep(4)
                        
                else:
                    # Moving down within same column
                    # Drag UP to reveal area below (bottom portion comes to center)
                    drag_x_mm = 0
                    drag_y_mm = base_drag_amount  # Drag up to show lower area
                    
                    print(f"\n{col*rows + row + 1}. Moving down in Column {col+1} to Row {row+1} (tile {row},{col})")
                    
                    if perform_drag(drag_x_mm, drag_y_mm, f"Move down: {drag_y_mm:.1f}mm up"):
                        time.sleep(4)
                
                # Capture tile
                capture_center_area(f"col_tile_{row}_{col}.png", f"Tile ({row},{col}) - Column {col+1}, Row {row+1}")
        
        # Verify capture order
        print("\n" + "="*60)
        print("COLUMN-BY-COLUMN CAPTURE VERIFICATION")
        print("="*60)
        
        expected_order = [
            (0, 0), (1, 0), (2, 0),  # Column 1: top to bottom
            (0, 1), (1, 1), (2, 1),  # Column 2: top to bottom  
            (0, 2), (1, 2), (2, 2)   # Column 3: top to bottom
        ]
        
        print("Expected capture order (column-by-column):")
        for i, (row, col) in enumerate(expected_order):
            print(f"  {i+1}. Tile ({row},{col}) - Column {col+1}, Row {row+1}")
        
        # Check captured files
        print("\nCaptured files:")
        success_count = 0
        for i, (row, col) in enumerate(expected_order):
            filename = f"col_tile_{row}_{col}.png"
            if os.path.exists(filename):
                size = os.path.getsize(filename)
                print(f"  ✓ {i+1}. {filename}: {size} bytes")
                success_count += 1
            else:
                print(f"  ✗ {i+1}. {filename}: Missing")
        
        print(f"\nCapture success rate: {success_count}/{len(expected_order)}")
        
        # Test dragging logic summary
        print("\n" + "="*60)
        print("DRAGGING LOGIC SUMMARY")
        print("="*60)
        
        print("Column-by-Column Traversal Rules:")
        print("1. Complete each column from top to bottom")
        print("2. When moving to next column:")
        print("   - Reset to top by dragging DOWN")
        print("   - Move right by dragging LEFT")
        print("3. When moving down in column:")
        print("   - Drag UP to show bottom area")
        print("4. Grid order: (0,0)→(1,0)→(2,0)→(0,1)→(1,1)→(2,1)→...")
        
        if success_count == len(expected_order):
            print("\n✓ Column-by-column traversal working correctly!")
            print("✓ Dragging logic properly implemented")
            print("✓ All tiles captured in correct order")
        else:
            print("\n⚠ Some issues with column-by-column traversal")
        
        print("\nTest complete. Browser will close in 10 seconds...")
        time.sleep(10)
        
        browser.close()
        
        return success_count == len(expected_order)


if __name__ == "__main__":
    success = test_column_traversal()
    print(f"\nColumn Traversal Test: {'PASSED' if success else 'FAILED'}")
