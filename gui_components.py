"""
GUI Components and Utilities for Screenshot Grid Application
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os


class InputFrame:
    """Frame containing all input fields for the application"""
    
    def __init__(self, parent):
        self.frame = ttk.LabelFrame(parent, text="Configuration", padding="10")
        self.vars = {}
        self.entries = {}
        self.create_widgets()
    
    def create_widgets(self):
        """Create all input widgets"""
        # URL Input
        ttk.Label(self.frame, text="Web Map URL:").grid(row=0, column=0, sticky="w", pady=2)
        self.vars['url'] = tk.StringVar(value="https://hsac.org.in/eodb/")
        self.entries['url'] = ttk.Entry(self.frame, textvariable=self.vars['url'], width=50)
        self.entries['url'].grid(row=0, column=1, columnspan=2, sticky="ew", pady=2)
        
        # Capture dimensions (in millimeters)
        ttk.Label(self.frame, text="Capture Size (mm):").grid(row=1, column=0, sticky="w", pady=2)
        self.vars['capture_size_mm'] = tk.DoubleVar(value=60.0)  # 60mm x 60mm default
        self.entries['capture_size_mm'] = ttk.Entry(self.frame, textvariable=self.vars['capture_size_mm'], width=15)
        self.entries['capture_size_mm'].grid(row=1, column=1, sticky="w", pady=2)

        # Add help text for capture size
        help_capture = ttk.Label(self.frame, text="Square capture area from screen center (60mm = ~227px)",
                                font=("Arial", 8), foreground="gray")
        help_capture.grid(row=1, column=1, columnspan=2, sticky="w", pady=(25, 2))

        # Sensitivity factor for drag amount
        ttk.Label(self.frame, text="Drag Sensitivity Factor:").grid(row=2, column=0, sticky="w", pady=2)
        self.vars['sensitivity'] = tk.DoubleVar(value=1.000)  # Default 1.000 (no change)
        self.entries['sensitivity'] = ttk.Entry(self.frame, textvariable=self.vars['sensitivity'], width=15)
        self.entries['sensitivity'].grid(row=2, column=1, sticky="w", pady=2)

        # Add help text for sensitivity
        help_sensitivity = ttk.Label(self.frame, text="Multiplier for drag amount (1.000 = normal, 0.500 = half, 2.000 = double)",
                                    font=("Arial", 8), foreground="gray")
        help_sensitivity.grid(row=2, column=1, columnspan=2, sticky="w", pady=(25, 2))

        # Add dynamic settings note
        dynamic_note = ttk.Label(self.frame, text="💡 Settings can be changed between captures during active session",
                               font=("Arial", 8), foreground="blue")
        dynamic_note.grid(row=10, column=0, columnspan=3, sticky="w", pady=(10, 0))
        
        # Grid dimensions
        ttk.Label(self.frame, text="Number of Rows:").grid(row=4, column=0, sticky="w", pady=2)
        self.vars['rows'] = tk.IntVar(value=2)
        self.entries['rows'] = ttk.Entry(self.frame, textvariable=self.vars['rows'], width=15)
        self.entries['rows'].grid(row=4, column=1, sticky="w", pady=2)

        ttk.Label(self.frame, text="Number of Columns:").grid(row=5, column=0, sticky="w", pady=2)
        self.vars['cols'] = tk.IntVar(value=2)
        self.entries['cols'] = ttk.Entry(self.frame, textvariable=self.vars['cols'], width=15)
        self.entries['cols'].grid(row=5, column=1, sticky="w", pady=2)
        
        # CSS Selector (optional)
        ttk.Label(self.frame, text="CSS Selector (optional):").grid(row=6, column=0, sticky="w", pady=2)
        self.vars['selector'] = tk.StringVar(value="")
        self.entries['selector'] = ttk.Entry(self.frame, textvariable=self.vars['selector'], width=50)
        self.entries['selector'].grid(row=6, column=1, columnspan=2, sticky="ew", pady=2)

        # Add help text for CSS selector
        help_label = ttk.Label(self.frame, text="Leave empty for mouse drag panning (works with any map)",
                              font=("Arial", 8), foreground="gray")
        help_label.grid(row=6, column=1, columnspan=2, sticky="w", pady=(25, 2))

        # Timing settings
        ttk.Label(self.frame, text="Initial Load Wait (sec):").grid(row=7, column=0, sticky="w", pady=2)
        self.vars['initial_wait'] = tk.DoubleVar(value=10.0)  # Default 10 seconds
        self.entries['initial_wait'] = ttk.Entry(self.frame, textvariable=self.vars['initial_wait'], width=15)
        self.entries['initial_wait'].grid(row=7, column=1, sticky="w", pady=2)

        ttk.Label(self.frame, text="Pan Render Wait (sec):").grid(row=8, column=0, sticky="w", pady=2)
        self.vars['pan_wait'] = tk.DoubleVar(value=20.0)  # Default 20 seconds as requested
        self.entries['pan_wait'] = ttk.Entry(self.frame, textvariable=self.vars['pan_wait'], width=15)
        self.entries['pan_wait'].grid(row=8, column=1, sticky="w", pady=2)

        # Output file path
        ttk.Label(self.frame, text="Output File Path:").grid(row=9, column=0, sticky="w", pady=2)
        self.vars['output_path'] = tk.StringVar(value="D:/Downloaded files/kapilmoond2.png")
        self.entries['output_path'] = ttk.Entry(self.frame, textvariable=self.vars['output_path'], width=40)
        self.entries['output_path'].grid(row=9, column=1, sticky="ew", pady=2)

        self.browse_btn = ttk.Button(self.frame, text="Browse...", command=self.browse_output_file)
        self.browse_btn.grid(row=9, column=2, sticky="w", pady=2, padx=(5, 0))
        
        # Configure column weights for resizing
        self.frame.columnconfigure(1, weight=1)
    
    def browse_output_file(self):
        """Open file dialog to select output file path"""
        filename = filedialog.asksaveasfilename(
            title="Save Stitched Image As",
            defaultextension=".png",
            filetypes=[
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.vars['output_path'].set(filename)
    
    def get_values(self):
        """Get all input values as a dictionary"""
        # Convert mm to pixels (96 DPI standard)
        pixels_per_mm = 96 / 25.4  # ~3.78 pixels per mm
        capture_size_mm = self.vars['capture_size_mm'].get()
        capture_size_px = int(capture_size_mm * pixels_per_mm)
        sensitivity = round(self.vars['sensitivity'].get(), 3)  # Round to 3 decimal places

        return {
            'url': self.vars['url'].get().strip(),
            'capture_size_mm': capture_size_mm,
            'sensitivity': sensitivity,
            'width': capture_size_px,  # Square capture area
            'height': capture_size_px,  # Square capture area
            'rows': self.vars['rows'].get(),
            'cols': self.vars['cols'].get(),
            'selector': self.vars['selector'].get().strip(),
            'initial_wait': self.vars['initial_wait'].get(),
            'pan_wait': self.vars['pan_wait'].get(),
            'output_path': self.vars['output_path'].get().strip()
        }
    
    def validate_inputs(self):
        """Validate all input values"""
        values = self.get_values()
        errors = []
        
        if not values['url']:
            errors.append("URL is required")
        elif not values['url'].startswith(('http://', 'https://')):
            errors.append("URL must start with http:// or https://")
        
        if values['capture_size_mm'] <= 0:
            errors.append("Capture size must be positive")
        if values['capture_size_mm'] > 200:
            errors.append("Capture size too large (max 200mm)")

        if values['sensitivity'] <= 0:
            errors.append("Sensitivity factor must be positive")
        if values['sensitivity'] > 10:
            errors.append("Sensitivity factor too large (max 10.000)")
        if values['rows'] <= 0:
            errors.append("Rows must be positive")
        if values['cols'] <= 0:
            errors.append("Columns must be positive")
        if values['initial_wait'] < 0:
            errors.append("Initial wait must be non-negative")
        if values['pan_wait'] < 0:
            errors.append("Pan wait must be non-negative")
        
        if not values['output_path']:
            errors.append("Output path is required")
        else:
            # Check if directory exists
            output_dir = os.path.dirname(values['output_path'])
            if output_dir and not os.path.exists(output_dir):
                errors.append(f"Output directory does not exist: {output_dir}")
        
        return errors


class StatusDisplay:
    """Status display component with progress tracking"""
    
    def __init__(self, parent):
        self.frame = ttk.LabelFrame(parent, text="Status", padding="10")
        self.create_widgets()
    
    def create_widgets(self):
        """Create status display widgets"""
        self.status_text = tk.Text(self.frame, height=8, width=70, wrap=tk.WORD, state=tk.DISABLED)
        self.status_text.grid(row=0, column=0, sticky="nsew")
        
        # Scrollbar for status text
        scrollbar = ttk.Scrollbar(self.frame, orient="vertical", command=self.status_text.yview)
        scrollbar.grid(row=0, column=1, sticky="ns")
        self.status_text.configure(yscrollcommand=scrollbar.set)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(10, 0))
        
        # Configure weights
        self.frame.rowconfigure(0, weight=1)
        self.frame.columnconfigure(0, weight=1)
    
    def update_status(self, message, progress=None):
        """Update status message and optionally progress"""
        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)
        
        if progress is not None:
            self.progress_var.set(progress)
    
    def clear_status(self):
        """Clear all status messages"""
        self.status_text.config(state=tk.NORMAL)
        self.status_text.delete(1.0, tk.END)
        self.status_text.config(state=tk.DISABLED)
        self.progress_var.set(0)


class ControlButtons:
    """Control buttons for the application"""
    
    def __init__(self, parent, launch_callback, start_callback, close_callback):
        self.frame = ttk.Frame(parent)
        self.launch_callback = launch_callback
        self.start_callback = start_callback
        self.close_callback = close_callback
        self.create_widgets()
    
    def create_widgets(self):
        """Create control buttons"""
        self.launch_btn = ttk.Button(
            self.frame,
            text="Launch Browser",
            command=self.launch_callback,
            style="Accent.TButton"
        )
        self.launch_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.start_btn = ttk.Button(
            self.frame,
            text="Start Capture",
            command=self.start_callback,
            state=tk.DISABLED
        )
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.close_btn = ttk.Button(
            self.frame,
            text="Close Browser",
            command=self.close_callback,
            state=tk.DISABLED
        )
        self.close_btn.pack(side=tk.LEFT)
    
    def set_launch_enabled(self, enabled):
        """Enable/disable launch button"""
        self.launch_btn.config(state=tk.NORMAL if enabled else tk.DISABLED)
    
    def set_start_enabled(self, enabled):
        """Enable/disable start button"""
        self.start_btn.config(state=tk.NORMAL if enabled else tk.DISABLED)

    def set_close_enabled(self, enabled):
        """Enable/disable close button"""
        self.close_btn.config(state=tk.NORMAL if enabled else tk.DISABLED)
