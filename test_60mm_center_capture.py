"""
Test the new 60mm center-based capture logic for HSAC website
"""
from playwright.sync_api import sync_playwright
import time
import os


def test_60mm_center_capture():
    """Test 60mm center-based capture with correct grid traversal"""
    
    print("Testing 60mm Center-Based Capture for HSAC")
    print("=" * 60)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False, args=["--start-maximized"])
        page = browser.new_page()
        
        print("Navigating to HSAC website...")
        page.goto("https://hsac.org.in/eodb/")
        
        print("Waiting 10 seconds for map to load...")
        time.sleep(10)
        
        # Test parameters
        capture_size_mm = 60.0
        pixels_per_mm = 96 / 25.4  # ~3.78 pixels per mm
        capture_size_px = int(capture_size_mm * pixels_per_mm)  # ~227 pixels
        
        print(f"Capture size: {capture_size_mm}mm = {capture_size_px}px")
        
        def capture_center_area(filename):
            """Capture the center area of the screen"""
            viewport_size = page.viewport_size
            center_x = viewport_size['width'] // 2
            center_y = viewport_size['height'] // 2
            
            capture_x = center_x - (capture_size_px // 2)
            capture_y = center_y - (capture_size_px // 2)
            
            # Ensure bounds
            capture_x = max(0, min(capture_x, viewport_size['width'] - capture_size_px))
            capture_y = max(0, min(capture_y, viewport_size['height'] - capture_size_px))
            
            page.screenshot(
                path=filename,
                clip={
                    'x': capture_x,
                    'y': capture_y,
                    'width': capture_size_px,
                    'height': capture_size_px
                }
            )
            print(f"✓ Captured {capture_size_mm}mm x {capture_size_mm}mm from center: {filename}")
        
        def perform_drag(drag_x_mm, drag_y_mm, description):
            """Perform mouse drag with given mm amounts"""
            print(f"\n{description}: Dragging {drag_x_mm}mm x {drag_y_mm}mm")
            
            try:
                # Find map element
                map_element = page.locator('#map').first
                if map_element.is_visible():
                    bbox = map_element.bounding_box()
                    drag_center_x = bbox['x'] + bbox['width'] // 2
                    drag_center_y = bbox['y'] + bbox['height'] // 2
                else:
                    viewport = page.viewport_size
                    drag_center_x = viewport['width'] // 2
                    drag_center_y = viewport['height'] // 2
                
                # Convert mm to pixels
                delta_x_px = int(drag_x_mm * pixels_per_mm)
                delta_y_px = int(drag_y_mm * pixels_per_mm)
                
                print(f"  Drag center: ({drag_center_x}, {drag_center_y})")
                print(f"  Movement: {drag_x_mm}mm x {drag_y_mm}mm = {delta_x_px}px x {delta_y_px}px")
                
                # Perform drag
                page.mouse.move(drag_center_x, drag_center_y)
                page.mouse.down()
                page.mouse.move(
                    drag_center_x + delta_x_px,
                    drag_center_y + delta_y_px,
                    steps=15
                )
                page.mouse.up()
                
                print(f"  ✓ Drag completed")
                return True
                
            except Exception as e:
                print(f"  ✗ Drag failed: {str(e)}")
                return False
        
        # Test 2x2 grid capture sequence
        print("\n" + "="*60)
        print("TESTING 2x2 GRID CAPTURE SEQUENCE")
        print("="*60)
        
        # Capture (0,0) - Initial center area
        print("\n1. Capturing initial center area (0,0)...")
        capture_center_area("center_0_0.png")
        
        # Move to (0,1) - Drag LEFT to show RIGHT area
        print("\n2. Moving to (0,1) - Show area to the right...")
        if perform_drag(-capture_size_mm, 0, "Drag LEFT to show RIGHT"):
            time.sleep(4)  # Wait for map to respond
            capture_center_area("center_0_1.png")
        
        # Move to (1,0) - Reset RIGHT then drag UP to show BELOW
        print("\n3. Moving to (1,0) - Reset to left, then show area below...")
        if perform_drag(capture_size_mm, 0, "Reset: Drag RIGHT to leftmost"):
            time.sleep(4)
            if perform_drag(0, capture_size_mm, "Drag UP to show BELOW"):
                time.sleep(4)
                capture_center_area("center_1_0.png")
        
        # Move to (1,1) - Drag LEFT to show RIGHT area of bottom row
        print("\n4. Moving to (1,1) - Show bottom-right area...")
        if perform_drag(-capture_size_mm, 0, "Drag LEFT to show RIGHT"):
            time.sleep(4)
            capture_center_area("center_1_1.png")
        
        # Test 3x3 grid movement (just the logic, not full capture)
        print("\n" + "="*60)
        print("TESTING 3x3 GRID MOVEMENT LOGIC")
        print("="*60)
        
        movements_3x3 = [
            (0, 0, "Initial position"),
            (0, 1, "Drag LEFT 60mm (show right)"),
            (0, 2, "Drag LEFT 60mm (show right)"),
            (1, 0, "Reset: Drag RIGHT 120mm, then UP 60mm (show below)"),
            (1, 1, "Drag LEFT 60mm (show right)"),
            (1, 2, "Drag LEFT 60mm (show right)"),
            (2, 0, "Reset: Drag RIGHT 120mm, then UP 60mm (show below)"),
            (2, 1, "Drag LEFT 60mm (show right)"),
            (2, 2, "Drag LEFT 60mm (show right)")
        ]
        
        for row, col, description in movements_3x3:
            print(f"({row},{col}): {description}")
        
        # Check results
        print("\n" + "="*60)
        print("CAPTURE RESULTS")
        print("="*60)
        
        test_files = [
            "center_0_0.png",
            "center_0_1.png", 
            "center_1_0.png",
            "center_1_1.png"
        ]
        
        for filename in test_files:
            if os.path.exists(filename):
                size = os.path.getsize(filename)
                print(f"✓ {filename}: {size} bytes")
            else:
                print(f"✗ {filename}: Missing")
        
        success_count = sum(1 for f in test_files if os.path.exists(f))
        print(f"\nCapture success rate: {success_count}/{len(test_files)}")
        
        if success_count == len(test_files):
            print("✓ All center captures successful!")
            print("✓ 60mm center-based approach working correctly")
        else:
            print("⚠ Some captures failed")
        
        print("\nTest complete. Browser will close in 15 seconds...")
        print("You can inspect the captured images and map behavior.")
        time.sleep(15)
        
        browser.close()


if __name__ == "__main__":
    test_60mm_center_capture()
