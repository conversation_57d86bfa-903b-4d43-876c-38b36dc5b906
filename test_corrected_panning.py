"""
Test the corrected ArcGIS panning logic with proper coordinate handling
"""
from playwright.sync_api import sync_playwright
import time


def test_corrected_panning():
    """Test the corrected panning logic"""
    
    # Corrected JavaScript with proper coordinate handling
    test_pan_js = '''
    (() => {
        const deltaX = -100;
        const deltaY = -50;
        
        try {
            if (window.view && window.view.goTo && window.view.ready) {
                const currentCenter = window.view.center;
                const currentExtent = window.view.extent;
                
                // Calculate map units per pixel
                const mapUnitsPerPixelX = currentExtent.width / window.view.width;
                const mapUnitsPerPixelY = currentExtent.height / window.view.height;
                
                // Calculate offset in map units
                const offsetX = deltaX * mapUnitsPerPixelX;
                const offsetY = deltaY * mapUnitsPerPixelY;
                
                // Get current coordinates - use x,y for projected systems
                let currentX, currentY, coordType;
                
                // Check spatial reference to determine coordinate handling
                const spatialRef = window.view.spatialReference;
                const isWebMercator = spatialRef && (spatialRef.wkid === 102100 || spatialRef.wkid === 3857);
                
                if (isWebMercator || (currentCenter.x !== undefined && currentCenter.y !== undefined)) {
                    // Use x,y for projected coordinates (Web Mercator)
                    currentX = currentCenter.x;
                    currentY = currentCenter.y;
                    coordType = 'projected';
                } else if (currentCenter.longitude !== undefined && currentCenter.latitude !== undefined) {
                    // Use longitude/latitude for geographic coordinates
                    currentX = currentCenter.longitude;
                    currentY = currentCenter.latitude;
                    coordType = 'geographic';
                } else {
                    throw new Error('Unknown coordinate system');
                }
                
                // Calculate new center
                const newCenterX = currentX + offsetX;
                const newCenterY = currentY - offsetY;
                
                return {
                    success: true,
                    method: 'arcgis_corrected',
                    coordType: coordType,
                    spatialReference: spatialRef ? spatialRef.wkid : 'unknown',
                    isWebMercator: isWebMercator,
                    currentCenter: [currentX, currentY],
                    newCenter: [newCenterX, newCenterY],
                    offsetX: offsetX,
                    offsetY: offsetY,
                    mapUnitsPerPixelX: mapUnitsPerPixelX,
                    extentWidth: currentExtent.width,
                    viewWidth: window.view.width,
                    centerLonLat: [currentCenter.longitude, currentCenter.latitude],
                    centerXY: [currentCenter.x, currentCenter.y]
                };
            } else {
                return {
                    success: false,
                    error: 'ArcGIS view not ready'
                };
            }
        } catch (e) {
            return {
                success: false,
                error: e.message
            };
        }
    })()
    '''
    
    print("Testing Corrected ArcGIS Panning Logic")
    print("=" * 50)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False, args=["--start-maximized"])
        page = browser.new_page()
        
        print("Navigating to HSAC website...")
        page.goto("https://hsac.org.in/eodb/")
        
        print("Waiting for map to load...")
        time.sleep(10)
        
        print("Testing corrected panning logic...")
        result = page.evaluate(test_pan_js)
        
        print("\nResults:")
        print(f"Success: {result.get('success')}")
        
        if result.get('success'):
            print(f"Method: {result.get('method')}")
            print(f"Coordinate Type: {result.get('coordType')}")
            print(f"Spatial Reference: {result.get('spatialReference')}")
            print(f"Is Web Mercator: {result.get('isWebMercator')}")
            print(f"Center (lon/lat): {result.get('centerLonLat')}")
            print(f"Center (x/y): {result.get('centerXY')}")
            print(f"Current center (used): {result.get('currentCenter')}")
            print(f"New center: {result.get('newCenter')}")
            print(f"Offset X: {result.get('offsetX'):.2f}")
            print(f"Offset Y: {result.get('offsetY'):.2f}")
            print(f"Map units per pixel: {result.get('mapUnitsPerPixelX'):.2f}")
            
            # Check if coordinates are reasonable
            old_center = result.get('currentCenter', [0, 0])
            new_center = result.get('newCenter', [0, 0])
            
            x_diff = abs(new_center[0] - old_center[0])
            y_diff = abs(new_center[1] - old_center[1])
            
            print(f"\nCoordinate differences:")
            print(f"X: {x_diff:.2f}")
            print(f"Y: {y_diff:.2f}")
            
            # For Web Mercator, differences should be reasonable in meters
            coord_type = result.get('coordType')
            if coord_type == 'projected':
                if x_diff < 50000 and y_diff < 50000:  # Less than 50km
                    print("✓ Projected coordinate changes look reasonable!")
                else:
                    print("⚠ Projected coordinate changes seem too large")
            elif coord_type == 'geographic':
                if x_diff < 1 and y_diff < 1:  # Less than 1 degree
                    print("✓ Geographic coordinate changes look reasonable!")
                else:
                    print("⚠ Geographic coordinate changes seem too large")
                    
        else:
            print(f"Error: {result.get('error')}")
        
        print("\nTest complete. Browser will close in 5 seconds...")
        time.sleep(5)
        
        browser.close()


if __name__ == "__main__":
    test_corrected_panning()
