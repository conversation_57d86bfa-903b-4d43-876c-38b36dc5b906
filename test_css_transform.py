"""
Test CSS transform approach for ArcGIS map panning
"""
from playwright.sync_api import sync_playwright
import time
import os


def test_css_transform_panning():
    """Test CSS transform panning and screenshot capture"""
    
    print("Testing CSS Transform Panning for ArcGIS Maps")
    print("=" * 60)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False, args=["--start-maximized"])
        page = browser.new_page()
        
        print("Navigating to HSAC website...")
        page.goto("https://hsac.org.in/eodb/")
        
        print("Waiting for map to load...")
        time.sleep(10)
        
        # Test CSS transform panning
        pan_js = '''
        (() => {
            const deltaX = -100;
            const deltaY = -50;
            
            try {
                // Try to find the map surface element for ArcGIS
                const mapSurface = document.querySelector('.esri-view-surface') ||
                                 document.querySelector('.esri-view-root') ||
                                 document.querySelector('#map canvas') ||
                                 document.querySelector('#map');
                
                if (!mapSurface) {
                    throw new Error('No map element found');
                }
                
                // Get current transform
                let currentTransform = mapSurface.style.transform || '';
                let currentX = 0, currentY = 0;
                
                // Parse current translate values
                const translateMatch = currentTransform.match(/translate\\(([^,]+),\\s*([^)]+)\\)/);
                const translate3dMatch = currentTransform.match(/translate3d\\(([^,]+),\\s*([^,]+),\\s*([^)]+)\\)/);
                
                if (translate3dMatch) {
                    currentX = parseFloat(translate3dMatch[1]) || 0;
                    currentY = parseFloat(translate3dMatch[2]) || 0;
                } else if (translateMatch) {
                    currentX = parseFloat(translateMatch[1]) || 0;
                    currentY = parseFloat(translateMatch[2]) || 0;
                }
                
                // Calculate new position
                const newX = currentX + deltaX;
                const newY = currentY + deltaY;
                
                // Apply new transform
                mapSurface.style.transform = `translate(${newX}px, ${newY}px)`;
                
                return {
                    success: true,
                    method: 'css_transform',
                    element: mapSurface.tagName + (mapSurface.className ? '.' + mapSurface.className.split(' ')[0] : ''),
                    oldX: currentX,
                    oldY: currentY,
                    newX: newX,
                    newY: newY
                };
            } catch (e) {
                return {
                    success: false,
                    error: e.message
                };
            }
        })()
        '''
        
        print("Testing initial screenshot...")
        page.screenshot(path="test_initial.png", clip={'x': 300, 'y': 200, 'width': 400, 'height': 300})
        print("✓ Initial screenshot saved as test_initial.png")
        
        print("Applying CSS transform pan...")
        result = page.evaluate(pan_js)
        
        if result.get('success'):
            print(f"✓ Pan successful using {result.get('method')}")
            print(f"  Element: {result.get('element')}")
            print(f"  Transform: ({result.get('oldX')}, {result.get('oldY')}) → ({result.get('newX')}, {result.get('newY')})")
            
            print("Waiting for render...")
            time.sleep(3)
            
            print("Taking screenshot after pan...")
            page.screenshot(path="test_after_pan.png", clip={'x': 300, 'y': 200, 'width': 400, 'height': 300})
            print("✓ Post-pan screenshot saved as test_after_pan.png")
            
            # Apply another pan
            print("Applying second pan...")
            result2 = page.evaluate(pan_js)
            if result2.get('success'):
                print(f"✓ Second pan successful")
                time.sleep(3)
                page.screenshot(path="test_second_pan.png", clip={'x': 300, 'y': 200, 'width': 400, 'height': 300})
                print("✓ Second pan screenshot saved as test_second_pan.png")
            
        else:
            print(f"✗ Pan failed: {result.get('error')}")
        
        print("\nScreenshot files created:")
        for filename in ["test_initial.png", "test_after_pan.png", "test_second_pan.png"]:
            if os.path.exists(filename):
                size = os.path.getsize(filename)
                print(f"  {filename}: {size} bytes")
        
        print("\nTest complete. Browser will close in 10 seconds...")
        print("You can manually inspect the map behavior during this time.")
        time.sleep(10)
        
        browser.close()


if __name__ == "__main__":
    test_css_transform_panning()
