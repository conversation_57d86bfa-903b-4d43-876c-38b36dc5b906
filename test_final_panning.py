"""
Test the final improved ArcGIS panning logic
"""
from playwright.sync_api import sync_playwright
import time


def test_final_panning():
    """Test the final panning logic with coordinate system detection"""
    
    # Final JavaScript with coordinate system detection
    test_pan_js = '''
    (() => {
        const deltaX = -100;
        const deltaY = -50;
        
        try {
            if (window.view && window.view.goTo && window.view.ready) {
                const currentCenter = window.view.center;
                const currentExtent = window.view.extent;
                
                // Calculate map units per pixel
                const mapUnitsPerPixelX = currentExtent.width / window.view.width;
                const mapUnitsPerPixelY = currentExtent.height / window.view.height;
                
                // Calculate offset in map units
                const offsetX = deltaX * mapUnitsPerPixelX;
                const offsetY = deltaY * mapUnitsPerPixelY;
                
                // Get current coordinates (could be geographic or projected)
                let currentX, currentY, coordType;
                if (currentCenter.longitude !== undefined && currentCenter.latitude !== undefined) {
                    // Geographic coordinates
                    currentX = currentCenter.longitude;
                    currentY = currentCenter.latitude;
                    coordType = 'geographic';
                } else if (currentCenter.x !== undefined && currentCenter.y !== undefined) {
                    // Projected coordinates
                    currentX = currentCenter.x;
                    currentY = currentCenter.y;
                    coordType = 'projected';
                } else {
                    throw new Error('Unknown coordinate system');
                }
                
                // Calculate new center
                const newCenterX = currentX + offsetX;
                const newCenterY = currentY - offsetY;
                
                return {
                    success: true,
                    method: 'arcgis_final',
                    coordType: coordType,
                    currentCenter: [currentX, currentY],
                    newCenter: [newCenterX, newCenterY],
                    offsetX: offsetX,
                    offsetY: offsetY,
                    mapUnitsPerPixelX: mapUnitsPerPixelX,
                    mapUnitsPerPixelY: mapUnitsPerPixelY,
                    extentWidth: currentExtent.width,
                    viewWidth: window.view.width,
                    spatialReference: window.view.spatialReference ? window.view.spatialReference.wkid : 'unknown'
                };
            } else {
                return {
                    success: false,
                    error: 'ArcGIS view not ready'
                };
            }
        } catch (e) {
            return {
                success: false,
                error: e.message
            };
        }
    })()
    '''
    
    print("Testing Final ArcGIS Panning Logic")
    print("=" * 50)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False, args=["--start-maximized"])
        page = browser.new_page()
        
        print("Navigating to HSAC website...")
        page.goto("https://hsac.org.in/eodb/")
        
        print("Waiting for map to load...")
        time.sleep(10)
        
        print("Testing final panning logic...")
        result = page.evaluate(test_pan_js)
        
        print("\nResults:")
        print(f"Success: {result.get('success')}")
        
        if result.get('success'):
            print(f"Method: {result.get('method')}")
            print(f"Coordinate Type: {result.get('coordType')}")
            print(f"Spatial Reference: {result.get('spatialReference')}")
            print(f"Current center: {result.get('currentCenter')}")
            print(f"New center: {result.get('newCenter')}")
            print(f"Offset X: {result.get('offsetX'):.2f}")
            print(f"Offset Y: {result.get('offsetY'):.2f}")
            print(f"Map units per pixel: {result.get('mapUnitsPerPixelX'):.2f}")
            print(f"Extent width: {result.get('extentWidth'):.2f}")
            print(f"View width: {result.get('viewWidth')}")
            
            # Check if coordinates are reasonable
            old_center = result.get('currentCenter', [0, 0])
            new_center = result.get('newCenter', [0, 0])
            
            x_diff = abs(new_center[0] - old_center[0])
            y_diff = abs(new_center[1] - old_center[1])
            
            print(f"\nCoordinate differences:")
            print(f"X: {x_diff:.2f}")
            print(f"Y: {y_diff:.2f}")
            
            # For projected coordinates, differences should be in meters
            # For geographic, should be small decimal degrees
            coord_type = result.get('coordType')
            if coord_type == 'projected':
                if x_diff < 50000 and y_diff < 50000:  # Less than 50km
                    print("✓ Projected coordinate changes look reasonable!")
                else:
                    print("⚠ Projected coordinate changes seem too large")
            elif coord_type == 'geographic':
                if x_diff < 1 and y_diff < 1:  # Less than 1 degree
                    print("✓ Geographic coordinate changes look reasonable!")
                else:
                    print("⚠ Geographic coordinate changes seem too large")
                    
        else:
            print(f"Error: {result.get('error')}")
        
        print("\nTest complete. Browser will close in 5 seconds...")
        time.sleep(5)
        
        browser.close()


if __name__ == "__main__":
    test_final_panning()
