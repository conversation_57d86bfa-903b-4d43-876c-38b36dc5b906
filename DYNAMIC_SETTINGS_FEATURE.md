# Dynamic Settings Feature

## Overview

The **Dynamic Settings Feature** allows users to modify capture parameters between captures during an active browser session. This eliminates the need to close and reopen the browser when changing settings, providing a seamless workflow for varied capture requirements.

## Key Benefits

### Workflow Efficiency
- **No browser restart** required for settings changes
- **Instant parameter updates** applied to next capture
- **Seamless transitions** between different capture configurations
- **Time savings** of 10+ seconds per settings change

### Flexibility
- **Real-time adjustments** based on map content
- **Progressive refinement** from overview to detail captures
- **Adaptive sensitivity** for different zoom levels
- **Custom output paths** for organized file management

## Changeable Parameters

### ✅ Capture Size (mm)
- **Range**: 10mm - 200mm
- **Common values**: 40mm (detail), 60mm (standard), 80mm (overview)
- **Use case**: Adjust based on desired coverage area

### ✅ Sensitivity Factor
- **Range**: 0.001 - 10.000 (3 decimal places)
- **Common values**: 0.500 (high zoom), 1.000 (normal), 2.000 (low zoom)
- **Use case**: Fine-tune for different map zoom levels

### ✅ Grid Dimensions
- **Rows**: 1-20 tiles
- **Columns**: 1-20 tiles
- **Common grids**: 2x2 (overview), 3x3 (standard), 4x4 (detailed)
- **Use case**: Match grid size to area complexity

### ✅ Timing Settings
- **Pan Wait Time**: 1-60 seconds
- **Use case**: Adjust for map loading speed and complexity

### ✅ Output Configuration
- **File Path**: Any valid path and filename
- **Use case**: Organize captures by area, zoom level, or purpose

### ❌ Non-Changeable Parameters
- **URL**: Requires browser restart to change websites
- **Initial Load Wait**: Set once during browser launch
- **CSS Selector**: Map element detection set at launch

## Usage Examples

### Example 1: Progressive Detail Mapping
```
Session Start: Launch browser → HSAC loads

Capture 1: Overview
- Size: 80mm, Sensitivity: 1.500, Grid: 2x2
- Output: "area_overview.png"
- Result: Wide area coverage

Capture 2: Medium Detail  
- Size: 60mm, Sensitivity: 1.000, Grid: 3x3
- Output: "area_medium.png"
- Result: Balanced detail and coverage

Capture 3: High Detail
- Size: 40mm, Sensitivity: 0.700, Grid: 4x4
- Output: "area_detail.png"
- Result: Maximum detail capture

Session End: Close browser
```

### Example 2: Multi-Zone Survey
```
Session Start: Launch browser → Position to Zone A

Capture 1: Zone A
- Size: 60mm, Sensitivity: 1.000, Grid: 2x2
- Output: "zone_a_survey.png"

Settings Update: Change output path only
- Output: "zone_b_survey.png"
- Reposition map to Zone B

Capture 2: Zone B  
- Same settings, new location
- Output: "zone_b_survey.png"

Settings Update: Increase detail for Zone C
- Size: 40mm, Sensitivity: 0.800, Grid: 3x3
- Output: "zone_c_detail.png"
- Reposition map to Zone C

Capture 3: Zone C
- Higher detail capture
- Output: "zone_c_detail.png"
```

### Example 3: Sensitivity Calibration
```
Session Start: Launch browser → Position test area

Test 1: Default sensitivity
- Size: 60mm, Sensitivity: 1.000, Grid: 2x2
- Output: "test_1000.png"
- Check for gaps/overlaps

Test 2: Reduced sensitivity
- Size: 60mm, Sensitivity: 0.750, Grid: 2x2  
- Output: "test_0750.png"
- Compare tile alignment

Test 3: Optimal sensitivity
- Size: 60mm, Sensitivity: 0.850, Grid: 2x2
- Output: "test_0850.png"
- Perfect alignment achieved

Production: Use calibrated settings
- Size: 60mm, Sensitivity: 0.850, Grid: 4x4
- Output: "production_capture.png"
```

## Technical Implementation

### Parameter Storage
```python
class ScreenshotAutomation:
    def __init__(self, status_queue, start_event):
        self.current_params = None  # Stores active parameters
        
    def update_parameters(self, new_params):
        """Update changeable parameters during session"""
        if self.current_params:
            self.current_params.update({
                'capture_size_mm': new_params['capture_size_mm'],
                'sensitivity': new_params['sensitivity'],
                'width': new_params['width'],
                'height': new_params['height'],
                'rows': new_params['rows'],
                'cols': new_params['cols'],
                'pan_wait': new_params['pan_wait'],
                'output_path': new_params['output_path']
            })
            return True
        return False
```

### GUI Integration
```python
def start_capture(self):
    """Apply current GUI settings before capture"""
    if self.automation and self.automation.current_params:
        # Get current GUI values
        current_values = self.input_frame.get_values()
        
        # Validate inputs
        errors = self.input_frame.validate_inputs()
        if errors:
            show_error_dialog(errors)
            return
            
        # Update automation parameters
        self.automation.update_parameters(current_values)
```

### Validation Process
1. **Input Validation**: Check all parameters before update
2. **Range Checking**: Ensure values within acceptable limits
3. **Type Conversion**: Convert GUI strings to appropriate types
4. **Error Handling**: Display clear error messages for invalid inputs

## User Interface Enhancements

### Visual Feedback
- **Blue info text**: "💡 Settings can be changed between captures during active session"
- **Status messages**: "✓ Settings updated: 40mm, sensitivity 0.750, 3x3 grid"
- **Success dialog**: Shows what can be changed between captures

### Button States
| Session State | Launch Browser | Start Capture | Close Browser |
|---------------|----------------|---------------|---------------|
| **Initial** | Enabled | Disabled | Disabled |
| **Browser Ready** | Disabled | Enabled | Enabled |
| **Capturing** | Disabled | Disabled | Enabled |
| **Capture Complete** | Disabled | **Enabled** | Enabled |

### Input Field Behavior
- **Always editable**: All changeable parameters remain editable during session
- **Real-time validation**: Immediate feedback on invalid values
- **Auto-update**: Changes applied automatically on next capture

## Best Practices

### Planning Your Session
1. **Start broad**: Begin with larger capture size for overview
2. **Refine progressively**: Reduce size and increase grid for detail
3. **Test sensitivity**: Use small grids to calibrate movement
4. **Organize outputs**: Use descriptive filenames for each capture

### Sensitivity Optimization
1. **Initial test**: Start with 1.000 sensitivity
2. **Check alignment**: Look for gaps or overlaps in 2x2 grid
3. **Adjust incrementally**: Change by 0.100-0.200 steps
4. **Fine-tune**: Use 0.050 steps for precision
5. **Document values**: Note optimal sensitivity for each zoom level

### File Organization
```
project_folder/
├── overview/
│   ├── area_overview_2x2.png
│   └── region_wide_1x3.png
├── medium/
│   ├── zone_a_medium_3x3.png
│   └── zone_b_medium_3x3.png
└── detail/
    ├── building_detail_4x4.png
    └── boundary_detail_5x5.png
```

## Troubleshooting

### Settings Not Applied
**Symptoms**: New settings don't seem to take effect
**Solutions**:
- Check for validation errors in GUI
- Ensure browser session is active
- Verify parameters in status messages

### Invalid Parameter Values
**Symptoms**: Error dialog when starting capture
**Solutions**:
- Check sensitivity range (0.001-10.000)
- Verify capture size (10-200mm)
- Ensure grid dimensions are positive integers

### Performance Issues
**Symptoms**: Slow response when changing settings
**Solutions**:
- Close unnecessary applications
- Reduce grid size for testing
- Increase pan wait time for complex maps

## Advanced Workflows

### Batch Processing with Variations
1. **Setup base parameters**: Common settings for all captures
2. **Create parameter sets**: Different configurations for each area
3. **Sequential capture**: Apply each parameter set in sequence
4. **Quality control**: Review each capture before proceeding

### Sensitivity Mapping
1. **Create test grid**: 3x3 grid of different sensitivity values
2. **Document results**: Note optimal values for each zoom level
3. **Build lookup table**: Quick reference for future sessions
4. **Share findings**: Document optimal values for team use

## Conclusion

The Dynamic Settings Feature transforms the application from a single-configuration tool to a flexible, multi-purpose mapping solution. Users can now:

- **Adapt in real-time** to changing requirements
- **Optimize settings** without workflow interruption  
- **Capture multiple scales** in a single session
- **Maintain productivity** with seamless parameter updates

This feature is essential for professional mapping workflows where different areas require different capture strategies, making the application suitable for complex surveying and documentation projects.

**Key Success Metrics**:
- ✅ **Zero downtime** for settings changes
- ✅ **Instant parameter updates** 
- ✅ **Flexible workflow support**
- ✅ **Professional productivity** enhancement
