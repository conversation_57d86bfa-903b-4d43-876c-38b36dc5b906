"""
Interactive Web Map Grid Screenshot & Stitcher
A GUI application for capturing and stitching web map screenshots in a grid pattern.

Author: <PERSON><PERSON>
"""
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import queue
import sys
import os

# Import our custom modules
from gui_components import InputFrame, StatusDisplay, ControlButtons
from screenshot_automation import ScreenshotAutomation


class ScreenshotGridApp:
    """Main application class"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_variables()
        self.create_widgets()
        self.setup_queue_checker()
        
    def setup_window(self):
        """Configure main window"""
        self.root.title("Interactive Web Map Grid Screenshot & Stitcher")
        self.root.geometry("800x700")
        self.root.minsize(600, 500)
        
        # Configure style
        style = ttk.Style()
        if "clam" in style.theme_names():
            style.theme_use("clam")
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(1, weight=1)
    
    def setup_variables(self):
        """Initialize application variables"""
        self.automation_thread = None
        self.status_queue = queue.Queue()
        self.start_capture_event = threading.Event()
        self.automation = None
        
    def create_widgets(self):
        """Create and layout all GUI widgets"""
        # Title
        title_label = ttk.Label(
            self.root, 
            text="Interactive Web Map Grid Screenshot & Stitcher",
            font=("Arial", 16, "bold")
        )
        title_label.grid(row=0, column=0, pady=(10, 20), sticky="ew")
        
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.grid(row=1, column=0, sticky="nsew", padx=20, pady=(0, 20))
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Input frame
        self.input_frame = InputFrame(main_frame)
        self.input_frame.frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        
        # Status display
        self.status_display = StatusDisplay(main_frame)
        self.status_display.frame.grid(row=1, column=0, sticky="nsew", pady=(0, 10))
        
        # Control buttons
        self.control_buttons = ControlButtons(
            main_frame,
            self.launch_browser,
            self.start_capture,
            self.close_browser
        )
        self.control_buttons.frame.grid(row=2, column=0, pady=(0, 10))
        
        # Initial status
        self.status_display.update_status("Welcome, Moond Sahab! Enter parameters and launch browser to begin.", 0)
        
        # Help text
        help_text = (
            "CENTER-BASED CAPTURE for HSAC Digital Land Records:\n"
            "1. Configure capture size (default: 60mm x 60mm from screen center)\n"
            "2. Set drag sensitivity factor (1.000 = normal, 0.500 = half, 2.000 = double)\n"
            "3. Click 'Launch Browser' to open HSAC map\n"
            "4. Wait for map to fully load (10 seconds)\n"
            "5. Position the map so your DESIRED AREA is in the CENTER of the screen\n"
            "6. Click 'Start Capture' - app will capture center area first\n"
            "7. App drags map by (60mm × sensitivity) to reveal adjacent areas\n"
            "8. After completion, browser STAYS OPEN for next capture!\n\n"
            "SERPENTINE CAPTURE: Col1↓ Col2↑ Col3↓ (zigzag pattern, no resets needed)\n"
            "CORNER REFERENCE: Uses boundary corners for accurate positioning\n"
            "DYNAMIC SETTINGS: Change size, sensitivity, grid between captures!\n"
            "MULTIPLE CAPTURES: Position map → Adjust settings → Click 'Start Capture'\n"
            "SENSITIVITY FACTOR: Adjusts drag amount based on zoom level\n"
            "• High zoom (detailed view): Use lower sensitivity (0.300-0.700)\n"
            "• Low zoom (wide view): Use higher sensitivity (1.500-3.000)\n"
            "COLOR TRANSFORMATION: Use separate Color Tool for image processing\n"
            "Click 'Close Browser' when finished with all captures."
        )
        self.status_display.update_status(help_text)
    
    def setup_queue_checker(self):
        """Setup periodic queue checking for thread communication"""
        self.check_queue()
    
    def check_queue(self):
        """Check for messages from background thread"""
        try:
            while True:
                message_type, content, progress = self.status_queue.get_nowait()
                
                if message_type == 'status':
                    self.status_display.update_status(content, progress)
                elif message_type == 'signal':
                    self.handle_signal(content)
                    
        except queue.Empty:
            pass
        
        # Schedule next check
        self.root.after(100, self.check_queue)
    
    def handle_signal(self, signal_type):
        """Handle special signals from background thread"""
        if signal_type == "BROWSER_READY":
            self.control_buttons.set_start_enabled(True)
            self.control_buttons.set_close_enabled(True)
            self.status_display.update_status("✓ Browser is ready! Position map and click 'Start Capture' to begin grid capture.")

        elif signal_type == "PROCESS_COMPLETE":
            # Keep browser open for next capture
            self.control_buttons.set_start_enabled(True)  # Re-enable for next capture
            self.control_buttons.set_close_enabled(True)
            self.status_display.update_status("✓ Capture completed! You can adjust settings and capture again.")
            messagebox.showinfo("Success", "Grid capture completed!\n\nYou can now:\n• Adjust settings (size, sensitivity, grid, etc.)\n• Position map to new area\n• Click 'Start Capture' for next grid")

        elif signal_type == "ERROR":
            self.control_buttons.set_launch_enabled(True)
            self.control_buttons.set_start_enabled(False)
            self.control_buttons.set_close_enabled(False)
            self.status_display.update_status("✗ An error occurred. Check the messages above.")
            messagebox.showerror("Error", "An error occurred during the process. Please check the status messages.")
    
    def launch_browser(self):
        """Launch browser and start automation thread"""
        # Close existing browser if any
        if self.automation:
            self.close_browser()

        # Validate inputs
        errors = self.input_frame.validate_inputs()
        if errors:
            error_msg = "Please fix the following errors:\n\n" + "\n".join(f"• {error}" for error in errors)
            messagebox.showerror("Input Validation Error", error_msg)
            return

        # Get parameters
        params = self.input_frame.get_values()

        # Update UI state
        self.control_buttons.set_launch_enabled(False)
        self.control_buttons.set_start_enabled(False)
        self.control_buttons.set_close_enabled(False)
        self.start_capture_event.clear()
        self.status_display.clear_status()

        # Create automation instance
        self.automation = ScreenshotAutomation(self.status_queue, self.start_capture_event)

        # Start automation thread
        self.automation_thread = threading.Thread(
            target=self.automation.run_automation,
            args=(params,),
            daemon=True
        )
        self.automation_thread.start()

        self.status_display.update_status(f"Starting automation for Moond Sahab...")
    
    def start_capture(self):
        """Signal the automation thread to start capture with current settings"""
        # Update parameters if browser session is active
        if self.automation and self.automation.current_params:
            # Get current GUI values
            current_values = self.input_frame.get_values()

            # Validate current inputs
            errors = self.input_frame.validate_inputs()
            if errors:
                error_msg = "Please fix the following errors before capture:\n\n" + "\n".join(f"• {error}" for error in errors)
                messagebox.showerror("Input Validation Error", error_msg)
                return

            # Update automation parameters
            self.automation.update_parameters(current_values)
            self.status_display.update_status("Settings updated for this capture.")

        self.control_buttons.set_start_enabled(False)
        self.start_capture_event.set()
        self.status_display.update_status("Capture signal sent. Beginning automated grid capture...")

    def close_browser(self):
        """Close the browser and reset the application"""
        if self.automation:
            try:
                self.automation.stop_automation()  # Stop the automation loop
                self.automation._cleanup_all()     # Clean up resources
                self.control_buttons.set_launch_enabled(True)
                self.control_buttons.set_start_enabled(False)
                self.control_buttons.set_close_enabled(False)
                self.status_display.update_status("✓ Browser closed. Ready for new session.")
                self.automation = None
            except Exception as e:
                self.status_display.update_status(f"Error closing browser: {str(e)}")
        else:
            self.status_display.update_status("No browser session to close.")
    
    def on_closing(self):
        """Handle application closing"""
        if self.automation_thread and self.automation_thread.is_alive():
            if messagebox.askokcancel("Quit", "Automation is running. Do you want to quit anyway?"):
                # Force cleanup
                if self.automation:
                    self.automation._cleanup()
                self.root.destroy()
        else:
            self.root.destroy()
    
    def run(self):
        """Start the application"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()


def check_dependencies():
    """Check if required dependencies are installed"""
    missing_deps = []
    
    try:
        import playwright
    except ImportError:
        missing_deps.append("playwright")
    
    try:
        from PIL import Image
    except ImportError:
        missing_deps.append("Pillow")
    
    if missing_deps:
        error_msg = (
            f"Missing required dependencies: {', '.join(missing_deps)}\n\n"
            "Please install them using:\n"
            "pip install -r requirements.txt\n"
            "playwright install"
        )
        messagebox.showerror("Missing Dependencies", error_msg)
        return False
    
    return True


def main():
    """Main entry point"""
    print("Interactive Web Map Grid Screenshot & Stitcher")
    print("=" * 50)
    print("Welcome, Moond Sahab!")
    print("Starting GUI application...")
    
    # Check dependencies
    if not check_dependencies():
        return
    
    # Create and run application
    try:
        app = ScreenshotGridApp()
        app.run()
    except Exception as e:
        messagebox.showerror("Application Error", f"An unexpected error occurred:\n{str(e)}")
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
