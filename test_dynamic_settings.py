"""
Test the dynamic settings change functionality during active session
"""
import time
import threading
import queue
from screenshot_automation import ScreenshotAutomation


def test_dynamic_settings():
    """Test that settings can be changed between captures"""
    
    print("Testing Dynamic Settings Change During Session")
    print("=" * 60)
    
    # Create test parameters
    initial_params = {
        'url': 'https://hsac.org.in/eodb/',
        'capture_size_mm': 60.0,
        'sensitivity': 1.000,
        'width': 227,  # 60mm at 96 DPI
        'height': 227,
        'rows': 2,
        'cols': 2,
        'selector': '',
        'initial_wait': 10.0,
        'pan_wait': 20.0,
        'output_path': 'test_dynamic_capture_1.png'
    }
    
    # Create communication objects
    status_queue = queue.Queue()
    start_event = threading.Event()
    
    # Create automation instance
    automation = ScreenshotAutomation(status_queue, start_event)
    
    print("1. Starting automation with initial settings...")
    print(f"   Initial: {initial_params['capture_size_mm']}mm, sensitivity {initial_params['sensitivity']:.3f}, {initial_params['rows']}x{initial_params['cols']}")
    
    # Start automation thread
    automation_thread = threading.Thread(
        target=automation.run_automation,
        args=(initial_params,),
        daemon=True
    )
    automation_thread.start()
    
    # Monitor for browser ready
    browser_ready = False
    
    print("2. Waiting for browser to be ready...")
    
    while not browser_ready:
        try:
            message_type, content, progress = status_queue.get(timeout=2)
            
            if message_type == 'status':
                progress_str = f" ({progress:.1f}%)" if progress is not None else ""
                print(f"   STATUS{progress_str}: {content}")
                
            elif message_type == 'signal':
                print(f"   SIGNAL: {content}")
                
                if content == "BROWSER_READY":
                    browser_ready = True
                    print("\n" + "="*50)
                    print("BROWSER IS READY - TESTING DYNAMIC SETTINGS")
                    print("="*50)
                    break
                    
                elif content == "ERROR":
                    print("ERROR: Browser launch failed!")
                    return False
                    
        except queue.Empty:
            continue
    
    if not browser_ready:
        print("✗ Browser failed to launch")
        return False
    
    # Test 1: First capture with initial settings
    print("\n3. Testing first capture with initial settings...")
    print(f"   Settings: {initial_params['capture_size_mm']}mm, sensitivity {initial_params['sensitivity']:.3f}")
    
    time.sleep(2)  # Simulate user positioning
    start_event.set()
    
    # Wait for first capture to complete
    first_complete = False
    while not first_complete:
        try:
            message_type, content, progress = status_queue.get(timeout=2)
            
            if message_type == 'status':
                progress_str = f" ({progress:.1f}%)" if progress is not None else ""
                print(f"   STATUS{progress_str}: {content}")
                
            elif message_type == 'signal':
                if content == "PROCESS_COMPLETE":
                    first_complete = True
                    print("✓ First capture completed")
                    break
                elif content == "ERROR":
                    print("✗ First capture failed")
                    return False
                    
        except queue.Empty:
            continue
    
    # Test 2: Update settings dynamically
    print("\n4. Testing dynamic settings update...")
    
    new_params = {
        'capture_size_mm': 40.0,  # Changed from 60mm
        'sensitivity': 0.750,     # Changed from 1.000
        'width': 151,             # 40mm at 96 DPI
        'height': 151,
        'rows': 3,                # Changed from 2
        'cols': 3,                # Changed from 2
        'pan_wait': 15.0,         # Changed from 20.0
        'output_path': 'test_dynamic_capture_2.png'  # Changed filename
    }
    
    print(f"   Updating to: {new_params['capture_size_mm']}mm, sensitivity {new_params['sensitivity']:.3f}, {new_params['rows']}x{new_params['cols']}")
    
    # Update parameters
    update_success = automation.update_parameters(new_params)
    
    if update_success:
        print("✓ Parameters updated successfully")
        
        # Verify parameters were updated
        if automation.current_params:
            current = automation.current_params
            print(f"   Verified: {current['capture_size_mm']}mm, sensitivity {current['sensitivity']:.3f}, {current['rows']}x{current['cols']}")
            
            # Check if all key parameters were updated
            params_match = (
                current['capture_size_mm'] == new_params['capture_size_mm'] and
                current['sensitivity'] == new_params['sensitivity'] and
                current['rows'] == new_params['rows'] and
                current['cols'] == new_params['cols']
            )
            
            if params_match:
                print("✓ All parameters updated correctly")
            else:
                print("✗ Parameter update verification failed")
                return False
        else:
            print("✗ No current parameters found")
            return False
    else:
        print("✗ Parameter update failed")
        return False
    
    # Test 3: Second capture with updated settings
    print("\n5. Testing second capture with updated settings...")
    
    time.sleep(3)  # Simulate user repositioning
    start_event.set()
    
    # Wait for second capture to complete
    second_complete = False
    while not second_complete:
        try:
            message_type, content, progress = status_queue.get(timeout=2)
            
            if message_type == 'status':
                progress_str = f" ({progress:.1f}%)" if progress is not None else ""
                print(f"   STATUS{progress_str}: {content}")
                
            elif message_type == 'signal':
                if content == "PROCESS_COMPLETE":
                    second_complete = True
                    print("✓ Second capture completed with updated settings")
                    break
                elif content == "ERROR":
                    print("✗ Second capture failed")
                    return False
                    
        except queue.Empty:
            continue
    
    # Test 4: Third settings update
    print("\n6. Testing another settings update...")
    
    third_params = {
        'capture_size_mm': 80.0,  # Changed again
        'sensitivity': 1.500,     # Changed again
        'width': 302,             # 80mm at 96 DPI
        'height': 302,
        'rows': 2,                # Back to 2x2
        'cols': 2,
        'pan_wait': 25.0,         # Increased wait time
        'output_path': 'test_dynamic_capture_3.png'
    }
    
    print(f"   Updating to: {third_params['capture_size_mm']}mm, sensitivity {third_params['sensitivity']:.3f}, {third_params['rows']}x{third_params['cols']}")
    
    update_success_2 = automation.update_parameters(third_params)
    
    if update_success_2:
        print("✓ Second parameter update successful")
    else:
        print("✗ Second parameter update failed")
        return False
    
    # Test results
    print("\n" + "="*60)
    print("DYNAMIC SETTINGS TEST RESULTS")
    print("="*60)
    
    results = {
        "Browser Launch": browser_ready,
        "First Capture": first_complete,
        "Settings Update 1": update_success,
        "Second Capture": second_complete,
        "Settings Update 2": update_success_2
    }
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name:<20} {status}")
    
    all_passed = all(results.values())
    
    print(f"\nOverall Result: {'✓ ALL TESTS PASSED' if all_passed else '✗ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n✓ Dynamic settings functionality working correctly!")
        print("✓ Users can change capture size, sensitivity, grid size, and output path")
        print("✓ Settings are applied immediately to next capture")
        print("✓ No need to restart browser for settings changes")
    else:
        print("\n✗ Dynamic settings functionality has issues")
    
    # Cleanup
    print("\n7. Cleaning up test...")
    try:
        automation.stop_automation()
        automation._cleanup_all()
        print("✓ Cleanup completed")
    except Exception as e:
        print(f"⚠ Cleanup error: {str(e)}")
    
    return all_passed


if __name__ == "__main__":
    success = test_dynamic_settings()
    print(f"\nTest {'PASSED' if success else 'FAILED'}")
