"""
Color Transformation Tool for Moond Sahab
Allows customizable color transformations with 5 color mappings
Supports image upload and download functionality
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, colorchooser
from PIL import Image
import numpy as np
import os


class ColorTransformationTool:
    """Standalone color transformation tool with customizable color mappings"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Color Transformation Tool - Moond Sahab")
        self.root.geometry("800x700")
        
        # Color mappings (from_color -> to_color)
        self.color_mappings = [
            {'from': (0, 0, 0), 'to': (255, 255, 255), 'enabled': True},      # Black → White
            {'from': (0, 0, 255), 'to': (255, 0, 0), 'enabled': True},        # Blue → Red
            {'from': (255, 255, 0), 'to': (0, 0, 0), 'enabled': True},        # Yellow → Black
            {'from': (0, 255, 0), 'to': (255, 0, 255), 'enabled': False},     # <PERSON> → Magenta
            {'from': (255, 0, 255), 'to': (0, 255, 0), 'enabled': False}      # Magenta → Green
        ]
        
        self.input_image = None
        self.output_image = None
        self.input_path = ""
        self.output_path = ""
        
        self.create_widgets()
        
    def create_widgets(self):
        """Create the GUI widgets"""
        # Title
        title_label = ttk.Label(self.root, text="🎨 Color Transformation Tool", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # Subtitle
        subtitle_label = ttk.Label(self.root, text="Transform colors in your images with custom mappings", 
                                  font=("Arial", 10))
        subtitle_label.pack(pady=(0, 20))
        
        # File operations frame
        file_frame = ttk.LabelFrame(self.root, text="📁 File Operations", padding="10")
        file_frame.pack(fill="x", padx=20, pady=10)
        
        # Input file
        input_frame = ttk.Frame(file_frame)
        input_frame.pack(fill="x", pady=5)
        
        ttk.Label(input_frame, text="Input Image:").pack(side="left")
        self.input_label = ttk.Label(input_frame, text="No file selected", foreground="gray")
        self.input_label.pack(side="left", padx=(10, 0))
        
        ttk.Button(input_frame, text="Browse...", command=self.browse_input_file).pack(side="right")
        
        # Output file
        output_frame = ttk.Frame(file_frame)
        output_frame.pack(fill="x", pady=5)
        
        ttk.Label(output_frame, text="Output Path:").pack(side="left")
        self.output_var = tk.StringVar(value="transformed_image.png")
        output_entry = ttk.Entry(output_frame, textvariable=self.output_var, width=40)
        output_entry.pack(side="left", padx=(10, 5), fill="x", expand=True)
        
        ttk.Button(output_frame, text="Browse...", command=self.browse_output_file).pack(side="right")
        
        # Color mappings frame
        mappings_frame = ttk.LabelFrame(self.root, text="🎨 Color Mappings", padding="10")
        mappings_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # Headers
        headers_frame = ttk.Frame(mappings_frame)
        headers_frame.pack(fill="x", pady=(0, 10))
        
        ttk.Label(headers_frame, text="Enable", width=8).pack(side="left")
        ttk.Label(headers_frame, text="From Color", width=15).pack(side="left", padx=(10, 0))
        ttk.Label(headers_frame, text="To Color", width=15).pack(side="left", padx=(10, 0))
        ttk.Label(headers_frame, text="Tolerance", width=10).pack(side="left", padx=(10, 0))
        
        # Color mapping rows
        self.mapping_widgets = []
        for i in range(5):
            self.create_color_mapping_row(mappings_frame, i)
        
        # Tolerance help
        help_label = ttk.Label(mappings_frame, 
                              text="💡 Tolerance: How close colors need to be to match (0-100, higher = more flexible)",
                              font=("Arial", 8), foreground="blue")
        help_label.pack(pady=(10, 0))
        
        # Control buttons frame
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill="x", padx=20, pady=20)
        
        # Transform button
        self.transform_btn = ttk.Button(control_frame, text="🔄 Transform Image", 
                                       command=self.transform_image, style="Accent.TButton")
        self.transform_btn.pack(side="left", padx=(0, 10))
        
        # Preview button
        self.preview_btn = ttk.Button(control_frame, text="👁 Preview", 
                                     command=self.preview_transformation, state="disabled")
        self.preview_btn.pack(side="left", padx=(0, 10))
        
        # Download button
        self.download_btn = ttk.Button(control_frame, text="💾 Download", 
                                      command=self.download_image, state="disabled")
        self.download_btn.pack(side="left", padx=(0, 10))
        
        # Reset button
        ttk.Button(control_frame, text="🔄 Reset", command=self.reset_mappings).pack(side="right")
        
        # Status label
        self.status_label = ttk.Label(self.root, text="Ready to transform images", 
                                     font=("Arial", 9), foreground="green")
        self.status_label.pack(pady=(0, 10))
        
    def create_color_mapping_row(self, parent, index):
        """Create a row for color mapping configuration"""
        row_frame = ttk.Frame(parent)
        row_frame.pack(fill="x", pady=2)
        
        mapping = self.color_mappings[index]
        
        # Enable checkbox
        enabled_var = tk.BooleanVar(value=mapping['enabled'])
        enabled_cb = ttk.Checkbutton(row_frame, variable=enabled_var, width=8)
        enabled_cb.pack(side="left")
        
        # From color button
        from_color = mapping['from']
        from_color_hex = f"#{from_color[0]:02x}{from_color[1]:02x}{from_color[2]:02x}"
        from_btn = tk.Button(row_frame, text=f"RGB{from_color}", bg=from_color_hex, 
                            width=15, command=lambda i=index: self.choose_from_color(i))
        from_btn.pack(side="left", padx=(10, 0))
        
        # Arrow
        ttk.Label(row_frame, text="→", width=3).pack(side="left", padx=5)
        
        # To color button
        to_color = mapping['to']
        to_color_hex = f"#{to_color[0]:02x}{to_color[1]:02x}{to_color[2]:02x}"
        to_btn = tk.Button(row_frame, text=f"RGB{to_color}", bg=to_color_hex, 
                          width=15, command=lambda i=index: self.choose_to_color(i))
        to_btn.pack(side="left", padx=(10, 0))
        
        # Tolerance
        tolerance_var = tk.IntVar(value=30)  # Default tolerance
        tolerance_spin = ttk.Spinbox(row_frame, from_=0, to=100, textvariable=tolerance_var, width=8)
        tolerance_spin.pack(side="left", padx=(10, 0))
        
        # Store widget references
        self.mapping_widgets.append({
            'enabled_var': enabled_var,
            'from_btn': from_btn,
            'to_btn': to_btn,
            'tolerance_var': tolerance_var,
            'enabled_cb': enabled_cb
        })
        
    def choose_from_color(self, index):
        """Choose 'from' color for mapping"""
        current_color = self.color_mappings[index]['from']
        color = colorchooser.askcolor(color=current_color, title=f"Choose 'From' Color {index+1}")
        if color[0]:  # color[0] is RGB tuple, color[1] is hex
            rgb = tuple(int(c) for c in color[0])
            self.color_mappings[index]['from'] = rgb
            self.update_color_button(self.mapping_widgets[index]['from_btn'], rgb)
            
    def choose_to_color(self, index):
        """Choose 'to' color for mapping"""
        current_color = self.color_mappings[index]['to']
        color = colorchooser.askcolor(color=current_color, title=f"Choose 'To' Color {index+1}")
        if color[0]:  # color[0] is RGB tuple, color[1] is hex
            rgb = tuple(int(c) for c in color[0])
            self.color_mappings[index]['to'] = rgb
            self.update_color_button(self.mapping_widgets[index]['to_btn'], rgb)
            
    def update_color_button(self, button, rgb_color):
        """Update color button appearance"""
        hex_color = f"#{rgb_color[0]:02x}{rgb_color[1]:02x}{rgb_color[2]:02x}"
        button.config(text=f"RGB{rgb_color}", bg=hex_color)
        
        # Set text color based on brightness
        brightness = sum(rgb_color) / 3
        text_color = "white" if brightness < 128 else "black"
        button.config(fg=text_color)
        
    def browse_input_file(self):
        """Browse for input image file"""
        filetypes = [
            ("Image files", "*.png *.jpg *.jpeg *.bmp *.tiff *.gif"),
            ("PNG files", "*.png"),
            ("JPEG files", "*.jpg *.jpeg"),
            ("All files", "*.*")
        ]
        
        filename = filedialog.askopenfilename(title="Select Input Image", filetypes=filetypes)
        if filename:
            self.input_path = filename
            self.input_label.config(text=os.path.basename(filename), foreground="black")
            self.status_label.config(text=f"Input image selected: {os.path.basename(filename)}", 
                                   foreground="blue")
            
            # Load and validate image
            try:
                self.input_image = Image.open(filename).convert('RGB')
                width, height = self.input_image.size
                self.status_label.config(text=f"Image loaded: {width}×{height} pixels", 
                                       foreground="green")
            except Exception as e:
                messagebox.showerror("Error", f"Could not load image: {str(e)}")
                self.input_image = None
                
    def browse_output_file(self):
        """Browse for output file location"""
        filetypes = [
            ("PNG files", "*.png"),
            ("JPEG files", "*.jpg"),
            ("All files", "*.*")
        ]
        
        filename = filedialog.asksaveasfilename(title="Save Transformed Image As", 
                                              defaultextension=".png", filetypes=filetypes)
        if filename:
            self.output_var.set(filename)
            
    def reset_mappings(self):
        """Reset color mappings to defaults"""
        defaults = [
            {'from': (0, 0, 0), 'to': (255, 255, 255), 'enabled': True},      # Black → White
            {'from': (0, 0, 255), 'to': (255, 0, 0), 'enabled': True},        # Blue → Red
            {'from': (255, 255, 0), 'to': (0, 0, 0), 'enabled': True},        # Yellow → Black
            {'from': (0, 255, 0), 'to': (255, 0, 255), 'enabled': False},     # Green → Magenta
            {'from': (255, 0, 255), 'to': (0, 255, 0), 'enabled': False}      # Magenta → Green
        ]
        
        for i, default in enumerate(defaults):
            self.color_mappings[i] = default.copy()
            self.mapping_widgets[i]['enabled_var'].set(default['enabled'])
            self.mapping_widgets[i]['tolerance_var'].set(30)
            self.update_color_button(self.mapping_widgets[i]['from_btn'], default['from'])
            self.update_color_button(self.mapping_widgets[i]['to_btn'], default['to'])
            
        self.status_label.config(text="Color mappings reset to defaults", foreground="blue")

    def apply_color_transformation(self, image):
        """Apply color transformations based on current mappings"""
        img_array = np.array(image)
        transformed = img_array.copy()

        transformations_applied = 0

        for i, mapping in enumerate(self.color_mappings):
            if not self.mapping_widgets[i]['enabled_var'].get():
                continue

            from_color = mapping['from']
            to_color = mapping['to']
            tolerance = self.mapping_widgets[i]['tolerance_var'].get()

            # Create mask for colors within tolerance
            diff = np.abs(img_array.astype(int) - np.array(from_color))
            mask = np.all(diff <= tolerance, axis=2)

            # Apply transformation
            transformed[mask] = to_color

            pixels_changed = np.sum(mask)
            if pixels_changed > 0:
                transformations_applied += 1
                print(f"Mapping {i+1}: {from_color} → {to_color}, {pixels_changed} pixels changed")

        return Image.fromarray(transformed), transformations_applied

    def transform_image(self):
        """Transform the input image"""
        if not self.input_image:
            messagebox.showerror("Error", "Please select an input image first")
            return

        try:
            self.status_label.config(text="Transforming image...", foreground="orange")
            self.root.update()

            # Apply transformations
            self.output_image, transformations_count = self.apply_color_transformation(self.input_image)

            # Enable preview and download buttons
            self.preview_btn.config(state="normal")
            self.download_btn.config(state="normal")

            self.status_label.config(text=f"✓ Transformation complete! {transformations_count} mappings applied",
                                   foreground="green")

        except Exception as e:
            messagebox.showerror("Error", f"Transformation failed: {str(e)}")
            self.status_label.config(text="Transformation failed", foreground="red")

    def preview_transformation(self):
        """Preview the transformed image"""
        if not self.output_image:
            messagebox.showwarning("Warning", "No transformed image to preview")
            return

        try:
            # Create preview window
            preview_window = tk.Toplevel(self.root)
            preview_window.title("Image Preview")
            preview_window.geometry("800x600")

            # Create notebook for before/after tabs
            notebook = ttk.Notebook(preview_window)
            notebook.pack(fill="both", expand=True, padx=10, pady=10)

            # Original image tab
            original_frame = ttk.Frame(notebook)
            notebook.add(original_frame, text="Original")

            original_label = ttk.Label(original_frame, text="Original Image")
            original_label.pack(pady=10)

            # Transformed image tab
            transformed_frame = ttk.Frame(notebook)
            notebook.add(transformed_frame, text="Transformed")

            transformed_label = ttk.Label(transformed_frame, text="Transformed Image")
            transformed_label.pack(pady=10)

            # Show image info
            width, height = self.output_image.size
            info_label = ttk.Label(preview_window, text=f"Image Size: {width} × {height} pixels")
            info_label.pack(pady=5)

            self.status_label.config(text="Preview window opened", foreground="blue")

        except Exception as e:
            messagebox.showerror("Error", f"Preview failed: {str(e)}")

    def download_image(self):
        """Download/save the transformed image"""
        if not self.output_image:
            messagebox.showwarning("Warning", "No transformed image to download")
            return

        output_path = self.output_var.get().strip()
        if not output_path:
            messagebox.showerror("Error", "Please specify an output path")
            return

        try:
            # Ensure directory exists
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # Save image
            self.output_image.save(output_path)

            file_size = os.path.getsize(output_path)
            self.status_label.config(text=f"✓ Image saved: {output_path} ({file_size} bytes)",
                                   foreground="green")

            # Ask if user wants to open the file location
            if messagebox.askyesno("Success", f"Image saved successfully!\n\nOpen file location?"):
                import subprocess
                import platform

                if platform.system() == "Windows":
                    subprocess.run(f'explorer /select,"{output_path}"')
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", "-R", output_path])
                else:  # Linux
                    subprocess.run(["xdg-open", output_dir])

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save image: {str(e)}")
            self.status_label.config(text="Save failed", foreground="red")

    def run(self):
        """Start the application"""
        self.root.mainloop()


if __name__ == "__main__":
    print("Color Transformation Tool for Moond Sahab")
    print("=" * 50)
    print("Starting Color Transformation Tool...")

    app = ColorTransformationTool()
    app.run()
