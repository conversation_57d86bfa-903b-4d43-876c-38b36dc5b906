"""
Test the serpentine/zigzag grid traversal logic
"""
from playwright.sync_api import sync_playwright
import time
import os


def test_serpentine_traversal():
    """Test serpentine grid traversal with optimized dragging"""
    
    print("Testing Serpentine/Zigzag Grid Traversal")
    print("=" * 60)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False, args=["--start-maximized"])
        page = browser.new_page()
        
        print("Navigating to HSAC website...")
        page.goto("https://hsac.org.in/eodb/")
        
        print("Waiting 10 seconds for map to load...")
        time.sleep(10)
        
        # Test parameters
        capture_size_mm = 60.0
        sensitivity = 1.000
        pixels_per_mm = 96 / 25.4  # ~3.78 pixels per mm
        capture_size_px = int(capture_size_mm * pixels_per_mm)  # ~227 pixels
        
        print(f"Test parameters: {capture_size_mm}mm capture, sensitivity {sensitivity:.3f}")
        
        def capture_center_area(filename, description):
            """Capture the center area of the screen"""
            viewport_size = page.viewport_size
            center_x = viewport_size['width'] // 2
            center_y = viewport_size['height'] // 2
            
            capture_x = center_x - (capture_size_px // 2)
            capture_y = center_y - (capture_size_px // 2)
            
            # Ensure bounds
            capture_x = max(0, min(capture_x, viewport_size['width'] - capture_size_px))
            capture_y = max(0, min(capture_y, viewport_size['height'] - capture_size_px))
            
            page.screenshot(
                path=filename,
                clip={
                    'x': capture_x,
                    'y': capture_y,
                    'width': capture_size_px,
                    'height': capture_size_px
                }
            )
            print(f"✓ {description}: {filename}")
        
        def perform_drag(drag_x_mm, drag_y_mm, description):
            """Perform mouse drag with given mm amounts"""
            print(f"\n{description}")
            print(f"  Dragging: {drag_x_mm:.1f}mm x {drag_y_mm:.1f}mm")
            
            try:
                # Find map element
                map_element = page.locator('#map').first
                if map_element.is_visible():
                    bbox = map_element.bounding_box()
                    drag_center_x = bbox['x'] + bbox['width'] // 2
                    drag_center_y = bbox['y'] + bbox['height'] // 2
                else:
                    viewport = page.viewport_size
                    drag_center_x = viewport['width'] // 2
                    drag_center_y = viewport['height'] // 2
                
                # Convert mm to pixels
                delta_x_px = int(drag_x_mm * pixels_per_mm)
                delta_y_px = int(drag_y_mm * pixels_per_mm)
                
                print(f"  Pixel movement: {delta_x_px}px x {delta_y_px}px")
                
                # Perform drag
                page.mouse.move(drag_center_x, drag_center_y)
                page.mouse.down()
                page.mouse.move(
                    drag_center_x + delta_x_px,
                    drag_center_y + delta_y_px,
                    steps=15
                )
                page.mouse.up()
                
                print(f"  ✓ Drag completed")
                return True
                
            except Exception as e:
                print(f"  ✗ Drag failed: {str(e)}")
                return False
        
        # Test 3x3 grid serpentine traversal
        print("\n" + "="*60)
        print("TESTING 3x3 GRID SERPENTINE TRAVERSAL")
        print("="*60)
        print("Order: (0,0)→(1,0)→(2,0)→(2,1)→(1,1)→(0,1)→(0,2)→(1,2)→(2,2)")
        print("Pattern: Col1↓ Col2↑ Col3↓")
        
        rows = 3
        cols = 3
        base_drag_amount = capture_size_mm * sensitivity
        
        # Define serpentine order
        serpentine_order = []
        for col in range(cols):
            if col % 2 == 0:
                # Even column: top to bottom
                for row in range(rows):
                    serpentine_order.append((row, col))
            else:
                # Odd column: bottom to top
                for row in range(rows - 1, -1, -1):
                    serpentine_order.append((row, col))
        
        print(f"\nSerpentine order: {serpentine_order}")
        
        # Capture sequence following serpentine logic
        for i, (row, col) in enumerate(serpentine_order):
            if row == 0 and col == 0:
                # Initial capture (0,0)
                print(f"\n1. Capturing initial tile (0,0) - Column 1↓, Row 1")
                capture_center_area(f"serp_tile_{row}_{col}.png", f"Tile ({row},{col})")
                continue
            
            # Determine if this is first tile of new column
            prev_row, prev_col = serpentine_order[i-1]
            is_new_column = (col != prev_col)
            
            if is_new_column:
                # Moving to new column - only horizontal movement
                drag_x_mm = -base_drag_amount  # Drag left to show right area
                drag_y_mm = 0
                
                direction = "↓" if col % 2 == 0 else "↑"
                print(f"\n{i+1}. Moving to Column {col+1}{direction} (tile {row},{col})")
                
                if perform_drag(drag_x_mm, drag_y_mm, f"Move to column {col+1}: {abs(drag_x_mm):.1f}mm left"):
                    time.sleep(4)
                    
            else:
                # Moving within same column
                if col % 2 == 0:
                    # Even column: moving down (drag UP to show bottom)
                    drag_x_mm = 0
                    drag_y_mm = base_drag_amount  # Drag up to show lower area
                    movement = "down"
                    direction_desc = f"{drag_y_mm:.1f}mm up to show bottom"
                else:
                    # Odd column: moving up (drag DOWN to show top)
                    drag_x_mm = 0
                    drag_y_mm = -base_drag_amount  # Drag down to show upper area
                    movement = "up"
                    direction_desc = f"{abs(drag_y_mm):.1f}mm down to show top"
                
                direction = "↓" if col % 2 == 0 else "↑"
                print(f"\n{i+1}. Moving {movement} in Column {col+1}{direction} to Row {row+1} (tile {row},{col})")
                
                if perform_drag(drag_x_mm, drag_y_mm, f"Move {movement}: {direction_desc}"):
                    time.sleep(4)
            
            # Capture tile
            direction = "↓" if col % 2 == 0 else "↑"
            capture_center_area(f"serp_tile_{row}_{col}.png", f"Tile ({row},{col}) - Column {col+1}{direction}, Row {row+1}")
        
        # Verify serpentine order
        print("\n" + "="*60)
        print("SERPENTINE TRAVERSAL VERIFICATION")
        print("="*60)
        
        print("Expected serpentine order:")
        for i, (row, col) in enumerate(serpentine_order):
            direction = "↓" if col % 2 == 0 else "↑"
            print(f"  {i+1}. Tile ({row},{col}) - Column {col+1}{direction}, Row {row+1}")
        
        # Check captured files
        print("\nCaptured files:")
        success_count = 0
        for i, (row, col) in enumerate(serpentine_order):
            filename = f"serp_tile_{row}_{col}.png"
            if os.path.exists(filename):
                size = os.path.getsize(filename)
                direction = "↓" if col % 2 == 0 else "↑"
                print(f"  ✓ {i+1}. {filename}: {size} bytes - Column {col+1}{direction}")
                success_count += 1
            else:
                print(f"  ✗ {i+1}. {filename}: Missing")
        
        print(f"\nCapture success rate: {success_count}/{len(serpentine_order)}")
        
        # Test movement efficiency
        print("\n" + "="*60)
        print("MOVEMENT EFFICIENCY ANALYSIS")
        print("="*60)
        
        print("Serpentine Pattern Benefits:")
        print("✓ No vertical resets between columns")
        print("✓ Continuous movement within columns") 
        print("✓ Only horizontal movement between columns")
        print("✓ Optimal path with minimal backtracking")
        
        print("\nMovement Summary:")
        print("Column 1 (0→1→2): 2 vertical moves (down)")
        print("Column 2 (2→1→0): 1 horizontal + 2 vertical moves (up)")  
        print("Column 3 (0→1→2): 1 horizontal + 2 vertical moves (down)")
        print("Total: 2 horizontal + 6 vertical = 8 moves (vs 10+ with resets)")
        
        if success_count == len(serpentine_order):
            print("\n✓ Serpentine traversal working correctly!")
            print("✓ Optimized dragging logic implemented")
            print("✓ All tiles captured in correct zigzag order")
            print("✓ No unnecessary resets between columns")
        else:
            print("\n⚠ Some issues with serpentine traversal")
        
        print("\nTest complete. Browser will close in 10 seconds...")
        time.sleep(10)
        
        browser.close()
        
        return success_count == len(serpentine_order)


if __name__ == "__main__":
    success = test_serpentine_traversal()
    print(f"\nSerpentine Traversal Test: {'PASSED' if success else 'FAILED'}")
