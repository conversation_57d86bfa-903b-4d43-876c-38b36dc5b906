"""
Test script to verify ArcGIS panning functionality
"""
from playwright.sync_api import sync_playwright
import time


def test_arcgis_panning():
    """Test the ArcGIS panning JavaScript function"""
    
    # JavaScript function for testing
    test_js = """
    (() => {
        const deltaX = -100;
        const deltaY = -50;
        
        // Method 1: Try ArcGIS MapView panning
        try {
            if (window.view && window.view.goTo) {
                // ArcGIS MapView detected
                const currentCenter = window.view.center;
                const currentExtent = window.view.extent;
                
                // Calculate new center based on pixel offset
                const pixelToMapRatio = (currentExtent.width / window.view.width);
                const newCenterX = currentCenter.longitude + (deltaX * pixelToMapRatio);
                const newCenterY = currentCenter.latitude - (deltaY * pixelToMapRatio);
                
                return {
                    method: 'arcgis',
                    detected: true,
                    currentCenter: [currentCenter.longitude, currentCenter.latitude],
                    newCenter: [newCenterX, newCenterY],
                    pixelToMapRatio: pixelToMapRatio,
                    viewWidth: window.view.width,
                    extentWidth: currentExtent.width
                };
            } else {
                return {
                    method: 'arcgis',
                    detected: false,
                    windowView: typeof window.view,
                    viewGoTo: window.view ? typeof window.view.goTo : 'view not found'
                };
            }
        } catch (e) {
            return {
                method: 'arcgis',
                detected: false,
                error: e.message
            };
        }
    })()
    """
    
    print("Testing ArcGIS panning on HSAC website...")
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        print("Navigating to HSAC website...")
        page.goto("https://hsac.org.in/eodb/")
        
        print("Waiting for page to load...")
        time.sleep(5)
        
        print("Testing ArcGIS detection...")
        result = page.evaluate(test_js)
        
        print("Test Results:")
        print(f"Method: {result.get('method')}")
        print(f"ArcGIS Detected: {result.get('detected')}")
        
        if result.get('detected'):
            print(f"Current Center: {result.get('currentCenter')}")
            print(f"New Center: {result.get('newCenter')}")
            print(f"Pixel to Map Ratio: {result.get('pixelToMapRatio')}")
            print(f"View Width: {result.get('viewWidth')}")
            print(f"Extent Width: {result.get('extentWidth')}")
        else:
            print(f"Window.view type: {result.get('windowView')}")
            print(f"View.goTo type: {result.get('viewGoTo')}")
            if result.get('error'):
                print(f"Error: {result.get('error')}")
        
        print("\nWaiting 10 seconds for manual inspection...")
        time.sleep(10)
        
        browser.close()


if __name__ == "__main__":
    test_arcgis_panning()
