"""
Test script to analyze the HSAC website structure
"""
from playwright.sync_api import sync_playwright
import time


def analyze_hsac_website():
    """Analyze the HSAC website to understand its ArcGIS implementation"""
    
    print("Analyzing HSAC Digital Land Records website...")
    print("=" * 60)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False, args=["--start-maximized"])
        page = browser.new_page()
        
        print("Navigating to HSAC website...")
        page.goto("https://hsac.org.in/eodb/")
        
        print("Waiting for initial page load...")
        time.sleep(8)
        
        print("\n1. Checking for ArcGIS elements...")
        
        # Check for window.view
        view_check = page.evaluate("typeof window.view")
        print(f"   window.view type: {view_check}")
        
        # Check if view is ready
        if view_check == "object":
            try:
                view_ready = page.evaluate("window.view && window.view.ready")
                print(f"   window.view.ready: {view_ready}")
                
                if view_ready:
                    center = page.evaluate("window.view.center ? [window.view.center.longitude, window.view.center.latitude] : null")
                    print(f"   Map center: {center}")
                    
                    extent = page.evaluate("window.view.extent ? window.view.extent.width : null")
                    print(f"   Extent width: {extent}")
                    
                    view_size = page.evaluate("[window.view.width, window.view.height]")
                    print(f"   View size: {view_size}")
            except Exception as e:
                print(f"   Error checking view properties: {e}")
        
        print("\n2. Checking DOM elements...")
        
        # Check for map container
        map_element = page.evaluate('document.querySelector("#map") ? "found" : "not found"')
        print(f"   #map element: {map_element}")
        
        # Check for esri elements
        esri_elements = page.evaluate('''
            Array.from(document.querySelectorAll('*')).filter(el => 
                el.className && typeof el.className === 'string' && el.className.includes('esri')
            ).map(el => el.tagName + '.' + el.className).slice(0, 10)
        ''')
        print(f"   Esri elements found: {len(esri_elements)}")
        for elem in esri_elements[:5]:
            print(f"     - {elem}")
        
        print("\n3. Checking map container properties...")
        
        # Check viewport size
        viewport = page.viewport_size
        print(f"   Viewport size: {viewport}")
        
        # Check map container size if exists
        try:
            map_bbox = page.evaluate('''
                const mapEl = document.querySelector('#map');
                if (mapEl) {
                    const rect = mapEl.getBoundingClientRect();
                    return {x: rect.x, y: rect.y, width: rect.width, height: rect.height};
                }
                return null;
            ''')
            print(f"   Map container bbox: {map_bbox}")
        except Exception as e:
            print(f"   Could not get map bbox: {e}")
        
        print("\n4. Testing pan functionality...")
        
        # Wait a bit more for map to fully load
        print("   Waiting for map to fully load...")
        time.sleep(5)
        
        # Test our panning JavaScript
        test_pan_js = '''
        (() => {
            const deltaX = -100;
            const deltaY = -50;
            
            try {
                if (window.view && window.view.goTo) {
                    const currentCenter = window.view.center;
                    const currentExtent = window.view.extent;
                    
                    const pixelToMapRatio = (currentExtent.width / window.view.width);
                    const newCenterX = currentCenter.longitude + (deltaX * pixelToMapRatio);
                    const newCenterY = currentCenter.latitude - (deltaY * pixelToMapRatio);
                    
                    return {
                        success: true,
                        method: 'arcgis',
                        currentCenter: [currentCenter.longitude, currentCenter.latitude],
                        newCenter: [newCenterX, newCenterY],
                        pixelToMapRatio: pixelToMapRatio
                    };
                } else {
                    return {
                        success: false,
                        error: 'ArcGIS view not available',
                        windowView: typeof window.view
                    };
                }
            } catch (e) {
                return {
                    success: false,
                    error: e.message
                };
            }
        })()
        '''
        
        try:
            pan_result = page.evaluate(test_pan_js)
            print(f"   Pan test result: {pan_result}")
            
            if pan_result.get('success'):
                print("   ✓ ArcGIS panning should work!")
                print(f"   Current center: {pan_result.get('currentCenter')}")
                print(f"   Pixel to map ratio: {pan_result.get('pixelToMapRatio')}")
            else:
                print(f"   ✗ Pan test failed: {pan_result.get('error')}")
                
        except Exception as e:
            print(f"   Error testing pan: {e}")
        
        print("\n5. Manual inspection time...")
        print("   The browser will stay open for 15 seconds for manual inspection.")
        print("   You can interact with the map to see how it behaves.")
        time.sleep(15)
        
        browser.close()
        
        print("\nAnalysis complete!")


if __name__ == "__main__":
    analyze_hsac_website()
