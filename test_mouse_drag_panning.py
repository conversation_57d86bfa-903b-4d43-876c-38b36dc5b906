"""
Test mouse drag panning approach for HSAC website
"""
from playwright.sync_api import sync_playwright
import time
import os


def test_mouse_drag_panning():
    """Test mouse drag panning approach"""
    
    print("Testing Mouse Drag Panning for HSAC Website")
    print("=" * 60)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False, args=["--start-maximized"])
        page = browser.new_page()
        
        print("Navigating to HSAC website...")
        page.goto("https://hsac.org.in/eodb/")
        
        print("Waiting for map to load...")
        time.sleep(10)
        
        def perform_mouse_drag_test(delta_x_mm, delta_y_mm, test_name):
            """Perform a mouse drag test"""
            print(f"\n{test_name}: Dragging {delta_x_mm}mm x {delta_y_mm}mm")
            
            try:
                # Find map element
                map_element = None
                selectors_to_try = ['#map', '.esri-view-surface', '.esri-view-root']
                
                for selector in selectors_to_try:
                    try:
                        element = page.locator(selector).first
                        if element.is_visible():
                            map_element = element
                            print(f"  Found map element: {selector}")
                            break
                    except:
                        continue
                
                if map_element:
                    # Get element bounding box
                    bbox = map_element.bounding_box()
                    if not bbox:
                        raise Exception("Could not get map element bounding box")
                    
                    # Calculate drag center
                    drag_center_x = bbox['x'] + bbox['width'] // 2
                    drag_center_y = bbox['y'] + bbox['height'] // 2
                    
                    print(f"  Map element bbox: {bbox}")
                    print(f"  Drag center: ({drag_center_x}, {drag_center_y})")
                else:
                    # Use viewport center
                    viewport = page.viewport_size
                    drag_center_x = viewport['width'] // 2
                    drag_center_y = viewport['height'] // 2
                    print(f"  Using viewport center: ({drag_center_x}, {drag_center_y})")
                
                # Convert mm to pixels (96 DPI)
                pixels_per_mm = 96 / 25.4  # ~3.78 pixels per mm
                delta_x_px = int(delta_x_mm * pixels_per_mm)
                delta_y_px = int(delta_y_mm * pixels_per_mm)
                
                print(f"  Converting {delta_x_mm}mm x {delta_y_mm}mm to {delta_x_px}px x {delta_y_px}px")
                
                # Perform mouse drag
                print("  Performing mouse drag...")
                page.mouse.move(drag_center_x, drag_center_y)
                page.mouse.down()
                page.mouse.move(
                    drag_center_x + delta_x_px, 
                    drag_center_y + delta_y_px, 
                    steps=15  # Smooth movement
                )
                page.mouse.up()
                
                print("  ✓ Mouse drag completed")
                return True
                
            except Exception as e:
                print(f"  ✗ Mouse drag failed: {str(e)}")
                return False
        
        # Take initial screenshot
        print("\nTaking initial screenshot...")
        page.screenshot(path="mouse_drag_initial.png", clip={'x': 400, 'y': 250, 'width': 400, 'height': 300})
        print("✓ Initial screenshot saved")
        
        # Test 1: Small drag (simulating tile movement)
        success1 = perform_mouse_drag_test(-26.5, -13.2, "Test 1 (Small Drag)")
        if success1:
            print("  Waiting for map to respond...")
            time.sleep(4)
            page.screenshot(path="mouse_drag_test1.png", clip={'x': 400, 'y': 250, 'width': 400, 'height': 300})
            print("  ✓ Test 1 screenshot saved")
        
        # Test 2: Medium drag
        success2 = perform_mouse_drag_test(-26.5, -13.2, "Test 2 (Medium Drag)")
        if success2:
            print("  Waiting for map to respond...")
            time.sleep(4)
            page.screenshot(path="mouse_drag_test2.png", clip={'x': 400, 'y': 250, 'width': 400, 'height': 300})
            print("  ✓ Test 2 screenshot saved")
        
        # Test 3: Reset drag (opposite direction)
        success3 = perform_mouse_drag_test(53.0, 0, "Test 3 (Reset Horizontal)")
        if success3:
            print("  Waiting for map to respond...")
            time.sleep(4)
            page.screenshot(path="mouse_drag_test3.png", clip={'x': 400, 'y': 250, 'width': 400, 'height': 300})
            print("  ✓ Test 3 screenshot saved")
        
        # Test 4: Vertical drag
        success4 = perform_mouse_drag_test(0, -13.2, "Test 4 (Vertical Drag)")
        if success4:
            print("  Waiting for map to respond...")
            time.sleep(4)
            page.screenshot(path="mouse_drag_test4.png", clip={'x': 400, 'y': 250, 'width': 400, 'height': 300})
            print("  ✓ Test 4 screenshot saved")
        
        # Check results
        print("\n" + "="*60)
        print("TEST RESULTS")
        print("="*60)
        
        test_results = [
            ("Initial", "mouse_drag_initial.png"),
            ("Test 1", "mouse_drag_test1.png"),
            ("Test 2", "mouse_drag_test2.png"), 
            ("Test 3", "mouse_drag_test3.png"),
            ("Test 4", "mouse_drag_test4.png")
        ]
        
        for test_name, filename in test_results:
            if os.path.exists(filename):
                size = os.path.getsize(filename)
                print(f"✓ {test_name}: {filename} ({size} bytes)")
            else:
                print(f"✗ {test_name}: {filename} (missing)")
        
        print(f"\nMouse drag success rate: {sum([success1, success2, success3, success4])}/4")
        
        if all([success1, success2, success3, success4]):
            print("✓ All mouse drag tests successful!")
            print("✓ Mouse drag approach should work for grid capture")
        else:
            print("⚠ Some mouse drag tests failed")
            print("⚠ May need to adjust timing or approach")
        
        print("\nTest complete. Browser will close in 10 seconds...")
        print("You can manually inspect the map behavior and screenshots.")
        time.sleep(10)
        
        browser.close()


if __name__ == "__main__":
    test_mouse_drag_panning()
