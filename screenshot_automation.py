"""
Screenshot Automation Module using <PERSON><PERSON> and <PERSON><PERSON>
"""
import os
import time
import shutil
import tempfile
from playwright.sync_api import sync_playwright
from PIL import Image
import threading
import queue


class ScreenshotAutomation:
    """Handles browser automation and screenshot stitching"""
    
    def __init__(self, status_queue, start_event):
        self.status_queue = status_queue
        self.start_event = start_event
        self.stop_event = threading.Event()  # Event to stop the automation loop
        self.browser = None
        self.page = None
        self.temp_dir = None
        self.current_params = None  # Store current parameters for updates
        
    def send_status(self, message, progress=None):
        """Send status update to GUI thread"""
        self.status_queue.put(('status', message, progress))
    
    def send_signal(self, signal_type):
        """Send special signal to GUI thread"""
        self.status_queue.put(('signal', signal_type, None))
    
    def run_automation(self, params):
        """Main automation workflow"""
        try:
            self.send_status("Initializing browser automation...", 0)

            # Store initial parameters
            self.current_params = params.copy()

            # Initialize Playwright
            with sync_playwright() as p:
                self.send_status("Launching browser...", 5)
                self.browser = p.chromium.launch(
                    headless=False,
                    args=["--start-maximized", "--disable-web-security"]
                )
                
                self.page = self.browser.new_page()
                
                # Navigate to URL
                self.send_status(f"Navigating to {params['url']}...", 10)
                self.page.goto(params['url'])
                
                # Wait for initial load
                self.send_status(f"Waiting {params['initial_wait']} seconds for page to load...", 15)
                time.sleep(params['initial_wait'])

                # Wait for ArcGIS view to be ready (specific for this site)
                self.send_status("Checking for ArcGIS MapView...", 18)
                arcgis_ready = False
                for attempt in range(10):  # Try for 10 seconds
                    try:
                        result = self.page.evaluate("window.view && window.view.ready")
                        if result:
                            arcgis_ready = True
                            self.send_status("✓ ArcGIS MapView detected and ready!")
                            break
                    except:
                        pass
                    time.sleep(1)

                if not arcgis_ready:
                    self.send_status("⚠ ArcGIS MapView not detected, will use fallback panning methods")

                # Signal browser is ready
                self.send_status("Browser ready. Position the map manually and click 'Start Capture'.", 20)
                self.send_signal("BROWSER_READY")
                
                # MULTIPLE CAPTURE LOOP - Keep browser alive
                capture_count = 0
                while not self.stop_event.is_set():
                    try:
                        # Wait for user to start capture or stop signal
                        self.start_event.wait()

                        # Check if stop was requested
                        if self.stop_event.is_set():
                            break

                        capture_count += 1

                        # Clear the event for next capture
                        self.start_event.clear()

                        # Begin capture process with current parameters
                        self.send_status(f"Starting capture #{capture_count} with current settings...", 25)
                        self._capture_grid(self.current_params)

                        # Stitch images
                        self.send_status("Stitching images together...", 90)
                        self._stitch_images(self.current_params)

                        # Clean up temporary files only
                        self.send_status("Cleaning up temporary files...", 95)
                        self._cleanup_temp_files()

                        # Complete - but keep browser open for next capture
                        self.send_status(f"✓ Capture #{capture_count} complete! Image saved to: {self.current_params['output_path']}", 100)
                        self.send_status("🔄 Browser remains open. You can adjust settings and click 'Start Capture' again.")
                        self.send_signal("PROCESS_COMPLETE")

                    except Exception as capture_error:
                        self.send_status(f"Capture #{capture_count} error: {str(capture_error)}", None)
                        self.send_signal("ERROR")
                        break  # Exit loop on capture error

        except Exception as e:
            self.send_status(f"Error: {str(e)}", None)
            self.send_signal("ERROR")
        # Note: No finally block - browser stays open for next capture
    
    def _capture_grid(self, params):
        """Capture grid of screenshots"""
        # Create temporary directory
        self.temp_dir = tempfile.mkdtemp(prefix="screenshot_grid_")
        
        total_tiles = params['rows'] * params['cols']
        current_tile = 0
        
        # Get element if selector provided
        element = None
        if params['selector']:
            try:
                element = self.page.locator(params['selector']).first
                if not element.is_visible():
                    raise Exception(f"Element with selector '{params['selector']}' is not visible")
            except Exception as e:
                raise Exception(f"Could not find element with selector '{params['selector']}': {str(e)}")
        
        # Capture first tile (0,0) - top-left corner
        self._capture_tile(0, 0, params, element)
        current_tile += 1
        progress = 25 + (current_tile / total_tiles) * 60
        self.send_status(f"✓ Captured tile (0,0) - Column 1, Row 1 of {total_tiles} total (serpentine pattern)", progress)
        
        # Define mouse drag panning function (MUCH MORE RELIABLE for ArcGIS maps)
        def perform_mouse_drag_pan(delta_x_mm, delta_y_mm):
            """
            Perform mouse drag panning using millimeter-based movements
            This simulates natural user interaction and works with any map type
            """
            try:
                # Get map element for dragging
                map_element = None
                selectors_to_try = ['#map', '.esri-view-surface', '.esri-view-root']

                for selector in selectors_to_try:
                    try:
                        element = self.page.locator(selector).first
                        if element.is_visible():
                            map_element = element
                            break
                    except:
                        continue

                if not map_element:
                    # Fallback to page-level drag
                    self.send_status("Using page-level mouse drag (no specific map element found)")

                    # Get viewport center for dragging
                    viewport = self.page.viewport_size
                    center_x = viewport['width'] // 2
                    center_y = viewport['height'] // 2

                    # Convert mm to pixels (assuming 96 DPI)
                    # 1 inch = 25.4 mm, 96 DPI = 96 pixels per inch
                    pixels_per_mm = 96 / 25.4  # ~3.78 pixels per mm

                    delta_x_px = int(delta_x_mm * pixels_per_mm)
                    delta_y_px = int(delta_y_mm * pixels_per_mm)

                    # Perform mouse drag on page
                    self.page.mouse.move(center_x, center_y)
                    self.page.mouse.down()
                    self.page.mouse.move(center_x + delta_x_px, center_y + delta_y_px, steps=10)
                    self.page.mouse.up()

                    return {
                        'success': True,
                        'method': 'page_mouse_drag',
                        'delta_mm': [delta_x_mm, delta_y_mm],
                        'delta_px': [delta_x_px, delta_y_px],
                        'drag_center': [center_x, center_y]
                    }
                else:
                    # Drag on specific map element
                    bbox = map_element.bounding_box()
                    if not bbox:
                        raise Exception("Could not get map element bounding box")

                    # Calculate drag center within map element
                    drag_center_x = bbox['x'] + bbox['width'] // 2
                    drag_center_y = bbox['y'] + bbox['height'] // 2

                    # Convert mm to pixels
                    pixels_per_mm = 96 / 25.4
                    delta_x_px = int(delta_x_mm * pixels_per_mm)
                    delta_y_px = int(delta_y_mm * pixels_per_mm)

                    # Perform mouse drag on map element
                    self.page.mouse.move(drag_center_x, drag_center_y)
                    self.page.mouse.down()
                    self.page.mouse.move(
                        drag_center_x + delta_x_px,
                        drag_center_y + delta_y_px,
                        steps=15  # Smooth movement
                    )
                    self.page.mouse.up()

                    return {
                        'success': True,
                        'method': 'element_mouse_drag',
                        'element_bbox': bbox,
                        'delta_mm': [delta_x_mm, delta_y_mm],
                        'delta_px': [delta_x_px, delta_y_px],
                        'drag_center': [drag_center_x, drag_center_y]
                    }

            except Exception as e:
                return {
                    'success': False,
                    'method': 'mouse_drag',
                    'error': str(e)
                }
        
        # SERPENTINE/ZIGZAG GRID TRAVERSAL LOGIC
        # Odd columns (1,3,5...): Top to bottom
        # Even columns (2,4,6...): Bottom to top
        # Grid order: (0,0)→(1,0)→(2,0)→(2,1)→(1,1)→(0,1)→(0,2)→(1,2)→(2,2)

        for col in range(params['cols']):
            # Determine direction for this column
            if col % 2 == 0:
                # Even column (0,2,4...): Top to bottom
                row_range = range(params['rows'])
                direction = "top-to-bottom"
            else:
                # Odd column (1,3,5...): Bottom to top
                row_range = range(params['rows'] - 1, -1, -1)
                direction = "bottom-to-top"

            for i, row in enumerate(row_range):
                if row == 0 and col == 0:
                    continue  # Already captured first tile (0,0)

                # Calculate drag direction based on grid movement with sensitivity factor
                base_drag_amount = params['capture_size_mm'] * params['sensitivity']

                if i == 0 and col > 0:
                    # Moving to start of next column
                    drag_x_mm = -base_drag_amount  # Drag left to show right area
                    drag_y_mm = 0

                    self.send_status(f"Moving to column {col+1} ({direction}): {abs(drag_x_mm):.3f}mm left to show right area (sensitivity: {params['sensitivity']:.3f})")

                else:
                    # Moving within column
                    drag_x_mm = 0

                    if col % 2 == 0:
                        # Even column: moving down (drag DOWN to show bottom)
                        drag_y_mm = -base_drag_amount  # Drag down to show lower area
                        self.send_status(f"Moving down in column {col+1} to row {row+1}: {abs(drag_y_mm):.3f}mm down to show bottom area (sensitivity: {params['sensitivity']:.3f})")
                    else:
                        # Odd column: moving up (drag UP to show top)
                        drag_y_mm = base_drag_amount  # Drag up to show upper area
                        self.send_status(f"Moving up in column {col+1} to row {row+1}: {drag_y_mm:.3f}mm up to show top area (sensitivity: {params['sensitivity']:.3f})")

                # Execute mouse drag pan with correct millimeter amounts
                pan_success = False
                pan_result = None

                for attempt in range(3):
                    try:
                        self.send_status(f"Mouse drag attempt {attempt + 1}: {drag_x_mm:.3f}mm x {drag_y_mm:.3f}mm")

                        pan_result = perform_mouse_drag_pan(drag_x_mm, drag_y_mm)

                        if pan_result and pan_result.get('success'):
                            method = pan_result.get('method', 'unknown')
                            delta_px = pan_result.get('delta_px', [0, 0])

                            self.send_status(f"✓ Dragged {drag_x_mm:.3f}mm x {drag_y_mm:.3f}mm ({delta_px[0]}px x {delta_px[1]}px)")

                            pan_success = True
                            break
                        else:
                            error_msg = pan_result.get('error', 'Unknown error') if pan_result else 'No result returned'
                            self.send_status(f"Mouse drag attempt {attempt + 1} failed: {error_msg}")

                    except Exception as e:
                        self.send_status(f"Mouse drag attempt {attempt + 1} exception: {str(e)}")

                    if not pan_success and attempt < 2:
                        self.send_status(f"Retrying mouse drag in 1 second...")
                        time.sleep(1)

                if not pan_success:
                    error_msg = f"All mouse drag attempts failed for tile {row},{col}."
                    if pan_result and pan_result.get('error'):
                        error_msg += f" Last error: {pan_result.get('error')}"

                    self.send_status(f"✗ {error_msg}")
                    # Continue with capture anyway - user might want partial results

                # Wait for render - critical for map tile loading after mouse drag
                render_wait = params['pan_wait']
                if pan_success:
                    # Mouse drag requires time for map to respond and load tiles
                    render_wait = max(params['pan_wait'], 3.0)  # Minimum 3 seconds for mouse drag

                self.send_status(f"Waiting {render_wait}s for map to respond to mouse drag...")
                time.sleep(render_wait)

                # Additional wait for any map animations or tile loading
                if pan_success:
                    try:
                        # Check if ArcGIS view is updating (if available)
                        updating_check = self.page.evaluate("""
                            (() => {{
                                // Check various possible updating states
                                if (window.view && typeof window.view.updating !== 'undefined') {{
                                    return window.view.updating;
                                }}
                                if (window.map && typeof window.map.updating !== 'undefined') {{
                                    return window.map.updating;
                                }}
                                // If no updating property available, assume not updating
                                return false;
                            }})()
                        """)

                        if updating_check:
                            self.send_status("Map is updating, waiting for completion...")
                            for wait_attempt in range(10):  # Wait up to 10 seconds
                                time.sleep(1)
                                still_updating = self.page.evaluate("""
                                    (window.view && window.view.updating) ||
                                    (window.map && window.map.updating) || false
                                """)
                                if not still_updating:
                                    break

                            if not still_updating:
                                self.send_status("✓ Map finished updating after mouse drag")
                            else:
                                self.send_status("⚠ Map still updating, proceeding with capture")
                        else:
                            self.send_status("✓ Map ready for capture")

                    except Exception as e:
                        self.send_status(f"Could not check map update status: {str(e)}")
                        # Add a small extra wait as fallback
                        time.sleep(1)

                # Capture tile
                try:
                    self._capture_tile(row, col, params, element)
                    current_tile += 1
                    progress = 25 + (current_tile / total_tiles) * 60
                    direction_info = "↓" if col % 2 == 0 else "↑"
                    self.send_status(f"✓ Captured tile ({row},{col}) - Column {col+1}{direction_info}, Row {row+1} ({current_tile}/{total_tiles})", progress)
                except Exception as e:
                    self.send_status(f"✗ Failed to capture tile ({row},{col}): {str(e)}")
                    # Continue with next tile even if one fails
    
    def _capture_tile(self, row, col, params, element):
        """Capture a single tile screenshot from the center of the screen"""
        filename = f"tile_{row}_{col}.png"
        filepath = os.path.join(self.temp_dir, filename)

        try:
            # ALWAYS capture from the CENTER of the viewport
            # This is the key - we capture the center area after each drag
            viewport_size = self.page.viewport_size

            # Calculate center position for capture
            center_x = viewport_size['width'] // 2
            center_y = viewport_size['height'] // 2

            # Calculate capture area around center
            capture_x = center_x - (params['width'] // 2)
            capture_y = center_y - (params['height'] // 2)

            # Ensure we don't go outside viewport bounds
            capture_x = max(0, min(capture_x, viewport_size['width'] - params['width']))
            capture_y = max(0, min(capture_y, viewport_size['height'] - params['height']))

            # Capture the center area
            self.page.screenshot(
                path=filepath,
                clip={
                    'x': capture_x,
                    'y': capture_y,
                    'width': params['width'],
                    'height': params['height']
                }
            )

            self.send_status(f"✓ Captured {params['capture_size_mm']}mm x {params['capture_size_mm']}mm from screen center")

        except Exception as e:
            self.send_status(f"✗ Capture error for tile {row},{col}: {str(e)}")
            raise e
    
    def _apply_color_transformation(self, image):
        """Apply color transformation: Black→White, Blue→Red, Yellow→Black"""
        import numpy as np

        # Convert PIL image to numpy array
        img_array = np.array(image)

        # Define color transformation rules
        # We'll use approximate color ranges for better matching

        # Create a copy for transformation
        transformed = img_array.copy()

        # Transform Black to White (RGB: 0,0,0 → 255,255,255)
        # Look for dark colors (close to black)
        black_mask = (img_array[:, :, 0] < 50) & (img_array[:, :, 1] < 50) & (img_array[:, :, 2] < 50)
        transformed[black_mask] = [255, 255, 255]  # White

        # Transform Blue to Red (RGB: 0,0,255 → 255,0,0)
        # Look for blue-ish colors (high blue, low red/green)
        blue_mask = (img_array[:, :, 2] > 150) & (img_array[:, :, 0] < 100) & (img_array[:, :, 1] < 100)
        transformed[blue_mask] = [255, 0, 0]  # Red

        # Transform Yellow to Black (RGB: 255,255,0 → 0,0,0)
        # Look for yellow-ish colors (high red and green, low blue)
        yellow_mask = (img_array[:, :, 0] > 200) & (img_array[:, :, 1] > 200) & (img_array[:, :, 2] < 100)
        transformed[yellow_mask] = [0, 0, 0]  # Black

        # Convert back to PIL image
        return Image.fromarray(transformed)

    def _stitch_images(self, params):
        """Stitch captured tiles into final image (column-by-column order)"""
        total_width = params['cols'] * params['width']
        total_height = params['rows'] * params['height']

        # Create final image
        final_image = Image.new('RGB', (total_width, total_height))

        # Paste tiles in correct positions
        # Note: tiles are captured column-by-column but stored with (row,col) naming
        for row in range(params['rows']):
            for col in range(params['cols']):
                filename = f"tile_{row}_{col}.png"
                filepath = os.path.join(self.temp_dir, filename)

                if os.path.exists(filepath):
                    tile_image = Image.open(filepath)
                    # Calculate correct position in final image
                    x_offset = col * params['width']   # Column position (left to right)
                    y_offset = row * params['height']  # Row position (top to bottom)
                    final_image.paste(tile_image, (x_offset, y_offset))
                    tile_image.close()
                    self.send_status(f"✓ Stitched tile ({row},{col}) at position ({x_offset},{y_offset})")
                else:
                    self.send_status(f"⚠ Missing tile file: {filename}")

        # Save final image (without color transformation)
        final_image.save(params['output_path'])
        final_image.close()
        self.send_status(f"✓ Final image saved: {total_width}×{total_height} pixels ({params['cols']}×{params['rows']} tiles)")
    
    def _cleanup_temp_files(self):
        """Clean up temporary files only (keep browser open)"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
                self.send_status("✓ Temporary files cleaned up")
            except Exception as e:
                self.send_status(f"⚠ Could not clean temp files: {str(e)}")
            self.temp_dir = None

    def update_parameters(self, new_params):
        """Update capture parameters during active session"""
        if self.current_params:
            # Update only the changeable parameters
            self.current_params.update({
                'capture_size_mm': new_params['capture_size_mm'],
                'sensitivity': new_params['sensitivity'],
                'width': new_params['width'],
                'height': new_params['height'],
                'rows': new_params['rows'],
                'cols': new_params['cols'],
                'pan_wait': new_params['pan_wait'],
                'output_path': new_params['output_path']
            })
            self.send_status(f"✓ Settings updated: {new_params['capture_size_mm']}mm, sensitivity {new_params['sensitivity']:.3f}, {new_params['rows']}x{new_params['cols']} grid")
            return True
        return False

    def stop_automation(self):
        """Stop the automation loop and close browser"""
        self.stop_event.set()
        self.start_event.set()  # Wake up the waiting thread

    def _cleanup_all(self):
        """Clean up all resources including browser"""
        # Stop the automation loop
        self.stop_event.set()

        # Clean temp files first
        self._cleanup_temp_files()

        # Close browser
        if self.browser:
            try:
                self.browser.close()
                self.send_status("✓ Browser closed")
            except Exception as e:
                self.send_status(f"⚠ Could not close browser: {str(e)}")
            self.browser = None
            self.page = None
